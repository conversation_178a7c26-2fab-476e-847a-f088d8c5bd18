1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.weinuo.quickcommands"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
12-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:6:5-7:53
12-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
13-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:8:5-9:47
13-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:8:22-75
14    <uses-permission
14-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:10:5-11:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:10:22-78
16        android:maxSdkVersion="29" />
16-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:11:9-35
17    <uses-permission
17-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:12:5-13:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:12:22-77
19        android:maxSdkVersion="32" />
19-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:13:9-35
20    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
20-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:14:5-15:40
20-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:14:22-79
21    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
21-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:16:5-88
21-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:16:22-85
22    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
22-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:17:5-78
22-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:17:22-75
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:18:5-68
23-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:18:22-65
24    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
24-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:19:5-75
24-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:19:22-72
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:20:5-66
25-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:20:22-63
26
27    <!-- 音频相关权限 -->
28    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
28-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:23:5-80
28-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:23:22-77
29    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
29-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:24:5-85
29-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:24:22-82
30
31    <!-- 网络相关权限 -->
32    <uses-permission android:name="android.permission.INTERNET" />
32-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:27:5-67
32-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:27:22-64
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
33-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:28:5-79
33-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:28:22-76
34    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
34-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:29:5-76
34-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:29:22-73
35
36    <!-- 通知权限 (Android 13+) -->
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
37-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:32:5-77
37-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:32:22-74
38
39    <!-- 后台进程管理权限 -->
40    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
40-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:35:5-84
40-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:35:22-81
41
42    <!-- 前台服务权限 (Android 9+) -->
43    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
43-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:38:5-77
43-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:38:22-74
44
45    <!-- 前台服务类型权限 (Android 14+) -->
46    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
46-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:41:5-87
46-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:41:22-84
47
48    <!-- 精确闹钟权限 (Android 12+) - 用于时间条件监控 -->
49    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
49-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:44:5-79
49-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:44:22-76
50    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
50-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:45:5-74
50-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:45:22-71
51
52    <!-- 通信状态条件相关权限 -->
53    <uses-permission android:name="android.permission.READ_CALL_LOG" />
53-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:48:5-72
53-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:48:22-69
54    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
54-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:49:5-73
54-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:49:22-70
55    <uses-permission android:name="android.permission.CALL_PHONE" />
55-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:50:5-69
55-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:50:22-66
56    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
56-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:51:5-75
56-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:51:22-72
57    <uses-permission android:name="android.permission.READ_SMS" />
57-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:52:5-67
57-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:52:22-64
58    <uses-permission android:name="android.permission.SEND_SMS" />
58-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:53:5-67
58-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:53:22-64
59    <uses-permission android:name="android.permission.READ_CONTACTS" />
59-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:54:5-72
59-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:54:22-69
60
61    <!-- 账号相关权限 -->
62    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
62-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:57:5-71
62-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:57:22-68
63    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
63-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:58:5-74
63-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:58:22-71
64
65    <!-- 传感器状态条件相关权限 -->
66    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
66-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:61:5-79
66-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:61:22-76
67
68    <!-- 位置相关权限 -->
69    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
69-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:64:5-79
69-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:64:22-76
70    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
70-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:65:5-81
70-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:65:22-78
71
72    <!-- 蓝牙相关权限 -->
73    <uses-permission android:name="android.permission.BLUETOOTH" />
73-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:68:5-68
73-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:68:22-65
74    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
74-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:69:5-74
74-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:69:22-71
75    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
75-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:70:5-76
75-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:70:22-73
76    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
76-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:71:5-73
76-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:71:22-70
77
78    <!-- 设备管理器相关权限 -->
79    <uses-permission android:name="android.permission.BIND_DEVICE_ADMIN" />
79-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:74:5-76
79-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:74:22-73
80
81    <!-- 媒体相关权限 -->
82    <uses-permission android:name="android.permission.RECORD_AUDIO" />
82-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:77:5-71
82-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:77:22-68
83
84    <!-- 相机相关权限 -->
85    <uses-permission android:name="android.permission.CAMERA" />
85-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:80:5-65
85-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:80:22-62
86
87    <!-- 传感器相关的uses-feature声明 -->
88    <uses-feature
88-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:5-99
89        android:name="android.hardware.sensor.accelerometer"
89-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:19-71
90        android:required="false" />
90-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:72-96
91    <uses-feature
91-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:5-91
92        android:name="android.hardware.sensor.light"
92-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:19-63
93        android:required="false" />
93-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:64-88
94    <uses-feature
94-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:5-95
95        android:name="android.hardware.sensor.proximity"
95-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:19-67
96        android:required="false" />
96-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:68-92
97
98    <permission
98-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
99        android:name="com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
99-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
100        android:protectionLevel="signature" />
100-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
101
102    <uses-permission android:name="com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
102-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
102-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
103    <uses-permission android:name="moe.shizuku.manager.permission.API_V23" />
103-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:8:5-78
103-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:8:22-75
104
105    <application
105-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:87:5-544:19
106        android:allowBackup="true"
106-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:88:9-35
107        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
107-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
108        android:dataExtractionRules="@xml/data_extraction_rules"
108-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:89:9-65
109        android:debuggable="true"
110        android:extractNativeLibs="false"
111        android:fullBackupContent="@xml/backup_rules"
111-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:90:9-54
112        android:icon="@mipmap/ic_launcher"
112-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:91:9-43
113        android:label="@string/app_name"
113-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:92:9-41
114        android:roundIcon="@mipmap/ic_launcher_round"
114-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:93:9-54
115        android:supportsRtl="true"
115-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:94:9-35
116        android:theme="@style/Theme.QuickCommands" >
116-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:95:9-51
117        <activity
117-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:97:9-114:20
118            android:name="com.weinuo.quickcommands.MainActivity"
118-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:98:13-41
119            android:allowEmbedded="true"
119-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:102:13-41
120            android:exported="true"
120-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:99:13-36
121            android:label="@string/app_name"
121-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:100:13-45
122            android:launchMode="singleTask"
122-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:103:13-44
123            android:resizeableActivity="true"
123-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:104:13-46
124            android:theme="@style/Theme.QuickCommands" >
124-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:101:13-55
125            <intent-filter>
125-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:105:13-108:29
126                <action android:name="android.intent.action.MAIN" />
126-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:106:17-69
126-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:106:25-66
127
128                <category android:name="android.intent.category.LAUNCHER" />
128-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:107:17-77
128-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:107:27-74
129            </intent-filter>
130
131            <!-- 声明静态快捷方式 -->
132            <meta-data
132-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:111:13-113:53
133                android:name="android.app.shortcuts"
133-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:112:17-53
134                android:resource="@xml/shortcuts" />
134-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:113:17-50
135        </activity>
136
137        <!-- 气泡通知专用Activity -->
138        <activity
138-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:117:9-126:20
139            android:name="com.weinuo.quickcommands.ui.BubbleActivity"
139-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:118:13-46
140            android:allowEmbedded="true"
140-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:121:13-41
141            android:documentLaunchMode="always"
141-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:122:13-48
142            android:exported="false"
142-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:119:13-37
143            android:launchMode="singleTask"
143-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:125:13-44
144            android:resizeableActivity="true"
144-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:123:13-46
145            android:taskAffinity=""
145-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:124:13-36
146            android:theme="@style/Theme.QuickCommands" >
146-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:120:13-55
147        </activity>
148
149        <!-- 手势录制Activity -->
150        <activity
150-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:129:9-135:20
151            android:name="com.weinuo.quickcommands.ui.recording.GestureRecordingActivity"
151-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:130:13-66
152            android:exported="false"
152-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:131:13-37
153            android:launchMode="singleTask"
153-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:134:13-44
154            android:screenOrientation="portrait"
154-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:133:13-49
155            android:theme="@style/Theme.QuickCommands.NoActionBar" >
155-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:132:13-67
156        </activity>
157
158        <!-- 手势录制编辑Activity -->
159        <activity
159-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:138:9-144:20
160            android:name="com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity"
160-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:139:13-70
161            android:exported="false"
161-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:140:13-37
162            android:launchMode="singleTask"
162-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:143:13-44
163            android:screenOrientation="portrait"
163-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:142:13-49
164            android:theme="@style/Theme.QuickCommands" >
164-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:141:13-55
165        </activity>
166
167        <!-- 快捷指令执行器活动 -->
168        <activity
168-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:147:9-156:20
169            android:name="com.weinuo.quickcommands.shortcut.QuickCommandExecutorActivity"
169-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:148:13-66
170            android:exported="true"
170-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:149:13-36
171            android:launchMode="singleTask"
171-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:151:13-44
172            android:theme="@style/Theme.QuickCommands.NoActionBar" >
172-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:150:13-67
173            <intent-filter>
173-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:152:13-155:29
174                <action android:name="android.intent.action.VIEW" />
174-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:17-69
174-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:25-66
175
176                <category android:name="android.intent.category.DEFAULT" />
176-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:17-76
176-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:27-73
177            </intent-filter>
178        </activity>
179
180        <!-- 静态快捷方式处理活动 -->
181        <activity
181-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:159:9-168:20
182            android:name="com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity"
182-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:160:13-67
183            android:exported="true"
183-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:161:13-36
184            android:launchMode="singleTask"
184-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:163:13-44
185            android:theme="@style/Theme.QuickCommands.NoActionBar" >
185-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:162:13-67
186            <intent-filter>
186-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:152:13-155:29
187                <action android:name="android.intent.action.VIEW" />
187-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:17-69
187-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:25-66
188
189                <category android:name="android.intent.category.DEFAULT" />
189-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:17-76
189-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:27-73
190            </intent-filter>
191        </activity>
192
193        <!-- 桌面小组件点击处理活动 -->
194        <activity
194-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:171:9-180:20
195            android:name="com.weinuo.quickcommands.widget.WidgetClickHandlerActivity"
195-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:172:13-62
196            android:exported="true"
196-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:173:13-36
197            android:launchMode="singleTask"
197-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:175:13-44
198            android:theme="@style/Theme.QuickCommands.NoActionBar" >
198-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:174:13-67
199            <intent-filter>
199-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:152:13-155:29
200                <action android:name="android.intent.action.VIEW" />
200-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:17-69
200-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:25-66
201
202                <category android:name="android.intent.category.DEFAULT" />
202-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:17-76
202-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:27-73
203            </intent-filter>
204        </activity>
205
206        <!-- 快捷指令表单Activity -->
207        <activity
207-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:183:9-188:20
208            android:name="com.weinuo.quickcommands.ui.activities.QuickCommandFormActivity"
208-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:184:13-67
209            android:exported="false"
209-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:185:13-37
210            android:launchMode="singleTask"
210-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:187:13-44
211            android:theme="@style/Theme.QuickCommands" >
211-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:186:13-55
212        </activity>
213
214        <!-- 图标选择Activity -->
215        <activity
215-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:191:9-196:20
216            android:name="com.weinuo.quickcommands.ui.activities.IconSelectionActivity"
216-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:192:13-64
217            android:exported="false"
217-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:193:13-37
218            android:launchMode="singleTask"
218-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:195:13-44
219            android:theme="@style/Theme.QuickCommands" >
219-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:194:13-55
220        </activity>
221
222        <!-- 应用选择Activity -->
223        <activity
223-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:199:9-204:20
224            android:name="com.weinuo.quickcommands.ui.activities.AppSelectionActivity"
224-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:200:13-63
225            android:exported="false"
225-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:201:13-37
226            android:launchMode="singleTask"
226-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:203:13-44
227            android:theme="@style/Theme.QuickCommands" >
227-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:202:13-55
228        </activity>
229
230        <!-- 统一配置Activity -->
231        <activity
231-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:207:9-212:20
232            android:name="com.weinuo.quickcommands.ui.activities.UnifiedConfigurationActivity"
232-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:208:13-71
233            android:exported="false"
233-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:209:13-37
234            android:launchMode="singleTask"
234-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:211:13-44
235            android:theme="@style/Theme.QuickCommands" >
235-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:210:13-55
236        </activity>
237
238        <!-- 详细配置Activity -->
239        <activity
239-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:215:9-220:20
240            android:name="com.weinuo.quickcommands.ui.activities.DetailedConfigurationActivity"
240-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:216:13-72
241            android:exported="false"
241-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:217:13-37
242            android:launchMode="singleTask"
242-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:219:13-44
243            android:theme="@style/Theme.QuickCommands" >
243-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:218:13-55
244        </activity>
245
246        <!-- 联系人选择Activity -->
247        <activity
247-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:223:9-228:20
248            android:name="com.weinuo.quickcommands.ui.activities.ContactSelectionActivity"
248-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:224:13-67
249            android:exported="false"
249-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:225:13-37
250            android:launchMode="singleTask"
250-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:227:13-44
251            android:theme="@style/Theme.QuickCommands" >
251-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:226:13-55
252        </activity>
253
254        <!-- 联系人分组选择Activity -->
255        <activity
255-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:231:9-236:20
256            android:name="com.weinuo.quickcommands.ui.activities.ContactGroupSelectionActivity"
256-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:232:13-72
257            android:exported="false"
257-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:233:13-37
258            android:launchMode="singleTask"
258-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:235:13-44
259            android:theme="@style/Theme.QuickCommands" >
259-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:234:13-55
260        </activity>
261
262        <!-- 铃声选择Activity -->
263        <activity
263-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:239:9-244:20
264            android:name="com.weinuo.quickcommands.ui.activities.RingtoneSelectionActivity"
264-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:240:13-68
265            android:exported="false"
265-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:241:13-37
266            android:launchMode="singleTask"
266-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:243:13-44
267            android:theme="@style/Theme.QuickCommands" >
267-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:242:13-55
268        </activity>
269
270        <!-- 分享目标选择Activity -->
271        <activity
271-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:247:9-252:20
272            android:name="com.weinuo.quickcommands.ui.activities.ShareTargetSelectionActivity"
272-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:248:13-71
273            android:exported="false"
273-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:249:13-37
274            android:launchMode="singleTask"
274-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:251:13-44
275            android:theme="@style/Theme.QuickCommands" >
275-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:250:13-55
276        </activity>
277
278        <!-- 高级内存配置Activity -->
279        <activity
279-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:255:9-260:20
280            android:name="com.weinuo.quickcommands.ui.activities.AdvancedMemoryConfigActivity"
280-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:256:13-71
281            android:exported="false"
281-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:257:13-37
282            android:launchMode="singleTask"
282-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:259:13-44
283            android:theme="@style/Theme.QuickCommands" >
283-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:258:13-55
284        </activity>
285
286        <!-- 内存学习数据Activity -->
287        <activity
287-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:263:9-268:20
288            android:name="com.weinuo.quickcommands.ui.activities.MemoryLearningDataActivity"
288-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:264:13-69
289            android:exported="false"
289-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:265:13-37
290            android:launchMode="singleTask"
290-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:267:13-44
291            android:theme="@style/Theme.QuickCommands" >
291-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:266:13-55
292        </activity>
293
294        <!-- 高级清理策略配置Activity -->
295        <activity
295-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:271:9-276:20
296            android:name="com.weinuo.quickcommands.ui.activities.AdvancedCleanupStrategyActivity"
296-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:272:13-74
297            android:exported="false"
297-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:273:13-37
298            android:launchMode="singleTask"
298-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:275:13-44
299            android:theme="@style/Theme.QuickCommands" >
299-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:274:13-55
300        </activity>
301
302        <!-- 添加清理规则Activity -->
303        <activity
303-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:279:9-284:20
304            android:name="com.weinuo.quickcommands.ui.activities.AddCleanupRuleActivity"
304-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:280:13-65
305            android:exported="false"
305-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:281:13-37
306            android:launchMode="singleTask"
306-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:283:13-44
307            android:theme="@style/Theme.QuickCommands" >
307-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:282:13-55
308        </activity>
309
310        <!-- 智慧提醒详细配置Activity -->
311        <activity
311-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:287:9-292:20
312            android:name="com.weinuo.quickcommands.ui.activities.SmartReminderDetailConfigActivity"
312-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:288:13-76
313            android:exported="false"
313-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:289:13-37
314            android:launchMode="singleTask"
314-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:291:13-44
315            android:theme="@style/Theme.QuickCommands" >
315-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:290:13-55
316        </activity>
317
318        <!-- 通信状态详细配置Activity -->
319        <activity
319-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:295:9-300:20
320            android:name="com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity"
320-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:296:13-81
321            android:exported="false"
321-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:297:13-37
322            android:launchMode="singleTask"
322-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:299:13-44
323            android:theme="@style/Theme.QuickCommands" >
323-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:298:13-55
324        </activity>
325
326        <!-- 联系人范围选择Activity -->
327        <activity
327-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:303:9-308:20
328            android:name="com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity"
328-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:304:13-72
329            android:exported="false"
329-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:305:13-37
330            android:launchMode="singleTask"
330-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:307:13-44
331            android:theme="@style/Theme.QuickCommands" >
331-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:306:13-55
332        </activity>
333
334        <!-- 筛选模式选择Activity -->
335        <activity
335-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:311:9-316:20
336            android:name="com.weinuo.quickcommands.ui.activities.FilterModeSelectionActivity"
336-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:312:13-70
337            android:exported="false"
337-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:313:13-37
338            android:launchMode="singleTask"
338-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:315:13-44
339            android:theme="@style/Theme.QuickCommands" >
339-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:314:13-55
340        </activity>
341
342        <!-- 桌面小组件1 -->
343        <receiver
343-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:319:9-329:20
344            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget1"
344-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:320:13-58
345            android:exported="true"
345-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:321:13-36
346            android:label="@string/widget_1_label" >
346-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:322:13-51
347            <intent-filter>
347-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:323:13-325:29
348                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
348-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:17-84
348-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:25-81
349            </intent-filter>
350
351            <meta-data
351-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:326:13-328:75
352                android:name="android.appwidget.provider"
352-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:327:17-58
353                android:resource="@xml/one_click_command_widget_1_info" />
353-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:328:17-72
354        </receiver>
355
356        <!-- 桌面小组件2 -->
357        <receiver
357-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:332:9-342:20
358            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget2"
358-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:333:13-58
359            android:exported="true"
359-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:334:13-36
360            android:label="@string/widget_2_label" >
360-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:335:13-51
361            <intent-filter>
361-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:323:13-325:29
362                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
362-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:17-84
362-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:25-81
363            </intent-filter>
364
365            <meta-data
365-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:326:13-328:75
366                android:name="android.appwidget.provider"
366-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:327:17-58
367                android:resource="@xml/one_click_command_widget_2_info" />
367-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:328:17-72
368        </receiver>
369
370        <!-- 桌面小组件3 -->
371        <receiver
371-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:345:9-355:20
372            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget3"
372-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:346:13-58
373            android:exported="true"
373-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:347:13-36
374            android:label="@string/widget_3_label" >
374-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:348:13-51
375            <intent-filter>
375-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:323:13-325:29
376                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
376-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:17-84
376-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:25-81
377            </intent-filter>
378
379            <meta-data
379-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:326:13-328:75
380                android:name="android.appwidget.provider"
380-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:327:17-58
381                android:resource="@xml/one_click_command_widget_3_info" />
381-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:328:17-72
382        </receiver>
383
384        <!-- 桌面小组件4 -->
385        <receiver
385-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:358:9-368:20
386            android:name="com.weinuo.quickcommands.widget.OneClickCommandWidget4"
386-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:359:13-58
387            android:exported="true"
387-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:360:13-36
388            android:label="@string/widget_4_label" >
388-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:361:13-51
389            <intent-filter>
389-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:323:13-325:29
390                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
390-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:17-84
390-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:25-81
391            </intent-filter>
392
393            <meta-data
393-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:326:13-328:75
394                android:name="android.appwidget.provider"
394-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:327:17-58
395                android:resource="@xml/one_click_command_widget_4_info" />
395-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:328:17-72
396        </receiver>
397
398        <!-- 设备管理器接收器 -->
399        <receiver
399-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:371:9-379:20
400            android:name="com.weinuo.quickcommands.permission.DeviceAdminReceiver"
400-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:371:19-65
401            android:exported="true"
401-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:373:13-36
402            android:permission="android.permission.BIND_DEVICE_ADMIN" >
402-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:372:13-70
403            <meta-data
403-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:374:13-375:56
404                android:name="android.app.device_admin"
404-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:374:24-63
405                android:resource="@xml/device_admin" />
405-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:375:17-53
406
407            <intent-filter>
407-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:376:13-378:29
408                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
408-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:377:17-82
408-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:377:25-79
409            </intent-filter>
410        </receiver>
411
412        <!-- Shizuku UserService -->
413        <provider
414            android:name="rikka.shizuku.ShizukuProvider"
414-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:383:13-57
415            android:authorities="com.weinuo.quickcommands.shizuku"
415-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:384:13-59
416            android:enabled="true"
416-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:385:13-35
417            android:exported="true"
417-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:386:13-36
418            android:multiprocess="false"
418-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:387:13-41
419            android:permission="android.permission.INTERACT_ACROSS_USERS_FULL" />
419-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:388:13-79
420
421        <!-- 快捷指令服务 -->
422        <service
422-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:391:9-395:56
423            android:name="com.weinuo.quickcommands.service.QuickCommandsService"
423-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:392:13-57
424            android:enabled="true"
424-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:393:13-35
425            android:exported="false"
425-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:394:13-37
426            android:foregroundServiceType="dataSync" />
426-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:395:13-53
427
428        <!-- 闹钟悬浮窗服务 -->
429        <service
429-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:400:9-403:40
430            android:name="com.weinuo.quickcommands.service.AlarmOverlayService"
430-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:401:13-56
431            android:enabled="true"
431-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:402:13-35
432            android:exported="false" />
432-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:403:13-37
433
434        <!-- 触摸屏蔽悬浮窗服务 -->
435        <service
435-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:406:9-409:40
436            android:name="com.weinuo.quickcommands.service.TouchBlockOverlayService"
436-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:407:13-61
437            android:enabled="true"
437-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:408:13-35
438            android:exported="false" />
438-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:409:13-37
439
440        <!-- 剪贴板刷新叠加层服务 -->
441        <service
441-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:412:9-415:40
442            android:name="com.weinuo.quickcommands.service.ClipboardRefreshOverlayService"
442-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:413:13-67
443            android:enabled="true"
443-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:414:13-35
444            android:exported="false" />
444-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:415:13-37
445
446        <!-- 悬浮按钮服务 -->
447        <service
447-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:418:9-421:40
448            android:name="com.weinuo.quickcommands.service.FloatingButtonService"
448-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:419:13-58
449            android:enabled="true"
449-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:420:13-35
450            android:exported="false" />
450-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:421:13-37
451
452        <!-- 智慧提醒悬浮窗服务 -->
453        <service
453-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:424:9-427:40
454            android:name="com.weinuo.quickcommands.service.SmartReminderOverlayService"
454-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:425:13-64
455            android:enabled="true"
455-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:426:13-35
456            android:exported="false" />
456-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:427:13-37
457
458        <!-- 悬浮录制服务 -->
459        <service
459-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:430:9-433:40
460            android:name="com.weinuo.quickcommands.floating.FloatingRecordingService"
460-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:13-62
461            android:enabled="true"
461-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:432:13-35
462            android:exported="false" />
462-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:433:13-37
463
464        <!-- 悬浮加速球服务 -->
465        <service
465-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:436:9-439:40
466            android:name="com.weinuo.quickcommands.floating.FloatingAcceleratorService"
466-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:437:13-64
467            android:enabled="true"
467-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:438:13-35
468            android:exported="false" />
468-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:439:13-37
469
470        <!-- 高级悬浮录制服务 -->
471        <service
471-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:442:9-445:40
472            android:name="com.weinuo.quickcommands.floating.AdvancedFloatingRecordingService"
472-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:443:13-70
473            android:enabled="true"
473-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:444:13-35
474            android:exported="false" />
474-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:445:13-37
475
476        <!-- 快捷指令-系统优先级提升无障碍服务 -->
477        <service
477-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:448:9-460:19
478            android:name="com.weinuo.quickcommands.service.SystemPriorityEnhancementService"
478-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:449:13-69
479            android:description="@string/accessibility_service_system_description"
479-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:451:13-83
480            android:exported="true"
480-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:453:13-36
481            android:label="@string/accessibility_service_name"
481-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:450:13-63
482            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
482-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:452:13-79
483            <intent-filter>
483-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:454:13-456:29
484                <action android:name="android.accessibilityservice.AccessibilityService" />
484-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:17-92
484-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:25-89
485            </intent-filter>
486
487            <meta-data
487-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:457:13-459:72
488                android:name="android.accessibilityservice"
488-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:458:17-60
489                android:resource="@xml/accessibility_service_config" />
489-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:459:17-69
490        </service>
491
492        <!-- 快捷指令-系统操作无障碍服务 -->
493        <service
493-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:463:9-475:19
494            android:name="com.weinuo.quickcommands.service.SystemOperationAccessibilityService"
494-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:464:13-72
495            android:description="@string/system_operation_accessibility_service_description"
495-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:466:13-93
496            android:exported="true"
496-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:468:13-36
497            android:label="@string/system_operation_accessibility_service_name"
497-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:465:13-80
498            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
498-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:467:13-79
499            <intent-filter>
499-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:454:13-456:29
500                <action android:name="android.accessibilityservice.AccessibilityService" />
500-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:17-92
500-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:25-89
501            </intent-filter>
502
503            <meta-data
503-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:457:13-459:72
504                android:name="android.accessibilityservice"
504-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:458:17-60
505                android:resource="@xml/system_operation_accessibility_service_config" />
505-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:459:17-69
506        </service>
507
508        <!-- 快捷指令-界面交互服务 -->
509        <service
509-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:478:9-490:19
510            android:name="com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService"
510-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:479:13-77
511            android:description="@string/interface_interaction_accessibility_service_description"
511-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:481:13-98
512            android:exported="true"
512-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:483:13-36
513            android:label="@string/interface_interaction_accessibility_service_name"
513-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:480:13-85
514            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
514-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:482:13-79
515            <intent-filter>
515-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:454:13-456:29
516                <action android:name="android.accessibilityservice.AccessibilityService" />
516-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:17-92
516-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:25-89
517            </intent-filter>
518
519            <meta-data
519-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:457:13-459:72
520                android:name="android.accessibilityservice"
520-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:458:17-60
521                android:resource="@xml/interface_interaction_accessibility_service_config" />
521-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:459:17-69
522        </service>
523
524        <!-- 快捷指令-手势识别服务 -->
525        <service
525-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:493:9-505:19
526            android:name="com.weinuo.quickcommands.service.GestureRecognitionAccessibilityService"
526-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:494:13-75
527            android:description="@string/gesture_recognition_accessibility_service_description"
527-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:496:13-96
528            android:exported="true"
528-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:498:13-36
529            android:label="@string/gesture_recognition_accessibility_service_name"
529-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:495:13-83
530            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
530-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:497:13-79
531            <intent-filter>
531-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:454:13-456:29
532                <action android:name="android.accessibilityservice.AccessibilityService" />
532-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:17-92
532-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:25-89
533            </intent-filter>
534
535            <meta-data
535-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:457:13-459:72
536                android:name="android.accessibilityservice"
536-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:458:17-60
537                android:resource="@xml/gesture_recognition_accessibility_service_config" />
537-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:459:17-69
538        </service>
539
540        <!-- 快捷指令-自动点击器服务 -->
541        <service
541-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:508:9-520:19
542            android:name="com.weinuo.quickcommands.service.AutoClickerAccessibilityService"
542-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:509:13-68
543            android:description="@string/auto_clicker_accessibility_service_description"
543-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:511:13-89
544            android:exported="true"
544-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:513:13-36
545            android:label="@string/auto_clicker_accessibility_service_name"
545-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:510:13-76
546            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
546-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:512:13-79
547            <intent-filter>
547-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:454:13-456:29
548                <action android:name="android.accessibilityservice.AccessibilityService" />
548-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:17-92
548-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:25-89
549            </intent-filter>
550
551            <meta-data
551-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:457:13-459:72
552                android:name="android.accessibilityservice"
552-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:458:17-60
553                android:resource="@xml/auto_clicker_accessibility_service_config" />
553-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:459:17-69
554        </service>
555
556        <!-- 通知监听服务 -->
557        <service
557-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:523:9-532:19
558            android:name="com.weinuo.quickcommands.service.QuickCommandsNotificationService"
558-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:524:13-69
559            android:description="@string/notification_listener_service_description"
559-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:526:13-84
560            android:exported="true"
560-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:528:13-36
561            android:label="@string/notification_listener_service_name"
561-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:525:13-71
562            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
562-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:527:13-87
563            <intent-filter>
563-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:529:13-531:29
564                <action android:name="android.service.notification.NotificationListenerService" />
564-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:530:17-99
564-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:530:25-96
565            </intent-filter>
566        </service>
567
568        <!-- FileProvider for camera file sharing -->
569        <provider
570            android:name="androidx.core.content.FileProvider"
570-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:536:13-62
571            android:authorities="com.weinuo.quickcommands.fileprovider"
571-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:537:13-64
572            android:exported="false"
572-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:538:13-37
573            android:grantUriPermissions="true" >
573-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:539:13-47
574            <meta-data
574-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:540:13-542:63
575                android:name="android.support.FILE_PROVIDER_PATHS"
575-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:541:17-67
576                android:resource="@xml/file_provider_paths" />
576-->D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:542:17-60
577        </provider>
578        <provider
578-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
579            android:name="androidx.startup.InitializationProvider"
579-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
580            android:authorities="com.weinuo.quickcommands.androidx-startup"
580-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
581            android:exported="false" >
581-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
582            <meta-data
582-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
583                android:name="androidx.emoji2.text.EmojiCompatInitializer"
583-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
584                android:value="androidx.startup" />
584-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
585            <meta-data
585-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
586                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
586-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
587                android:value="androidx.startup" />
587-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
588            <meta-data
588-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
589                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
589-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
590                android:value="androidx.startup" />
590-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
591        </provider>
592
593        <receiver
593-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
594            android:name="androidx.profileinstaller.ProfileInstallReceiver"
594-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
595            android:directBootAware="false"
595-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
596            android:enabled="true"
596-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
597            android:exported="true"
597-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
598            android:permission="android.permission.DUMP" >
598-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
599            <intent-filter>
599-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
600                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
600-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
600-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
601            </intent-filter>
602            <intent-filter>
602-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
603                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
603-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
603-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
604            </intent-filter>
605            <intent-filter>
605-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
606                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
606-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
606-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
607            </intent-filter>
608            <intent-filter>
608-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
609                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
609-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
609-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
610            </intent-filter>
611        </receiver>
612
613        <meta-data
613-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:11:9-13:36
614            android:name="moe.shizuku.client.V3_SUPPORT"
614-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:12:13-57
615            android:value="true" />
615-->[dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:13:13-33
616    </application>
617
618</manifest>
