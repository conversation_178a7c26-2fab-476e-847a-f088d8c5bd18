int attr action 0x7f010000
int attr alpha 0x7f010001
int attr argType 0x7f010002
int attr data 0x7f010003
int attr dataPattern 0x7f010004
int attr destination 0x7f010005
int attr enterAnim 0x7f010006
int attr exitAnim 0x7f010007
int attr font 0x7f010008
int attr fontProviderAuthority 0x7f010009
int attr fontProviderCerts 0x7f01000a
int attr fontProviderFallbackQuery 0x7f01000b
int attr fontProviderFetchStrategy 0x7f01000c
int attr fontProviderFetchTimeout 0x7f01000d
int attr fontProviderPackage 0x7f01000e
int attr fontProviderQuery 0x7f01000f
int attr fontProviderSystemFontFamily 0x7f010010
int attr fontStyle 0x7f010011
int attr fontVariationSettings 0x7f010012
int attr fontWeight 0x7f010013
int attr graph 0x7f010014
int attr lStar 0x7f010015
int attr launchSingleTop 0x7f010016
int attr mimeType 0x7f010017
int attr navGraph 0x7f010018
int attr nestedScrollViewStyle 0x7f010019
int attr nullable 0x7f01001a
int attr popEnterAnim 0x7f01001b
int attr popExitAnim 0x7f01001c
int attr popUpTo 0x7f01001d
int attr popUpToInclusive 0x7f01001e
int attr popUpToSaveState 0x7f01001f
int attr queryPatterns 0x7f010020
int attr restoreState 0x7f010021
int attr route 0x7f010022
int attr shortcutMatchRequired 0x7f010023
int attr startDestination 0x7f010024
int attr targetPackage 0x7f010025
int attr ttcIndex 0x7f010026
int attr uri 0x7f010027
int color androidx_core_ripple_material_light 0x7f020000
int color androidx_core_secondary_text_default_material_light 0x7f020001
int color black 0x7f020002
int color call_notification_answer_color 0x7f020003
int color call_notification_decline_color 0x7f020004
int color notification_action_color_filter 0x7f020005
int color notification_icon_bg_color 0x7f020006
int color purple_200 0x7f020007
int color purple_500 0x7f020008
int color purple_700 0x7f020009
int color teal_200 0x7f02000a
int color teal_700 0x7f02000b
int color vector_tint_color 0x7f02000c
int color vector_tint_theme_color 0x7f02000d
int color white 0x7f02000e
int dimen compat_button_inset_horizontal_material 0x7f030000
int dimen compat_button_inset_vertical_material 0x7f030001
int dimen compat_button_padding_horizontal_material 0x7f030002
int dimen compat_button_padding_vertical_material 0x7f030003
int dimen compat_control_corner_material 0x7f030004
int dimen compat_notification_large_icon_max_height 0x7f030005
int dimen compat_notification_large_icon_max_width 0x7f030006
int dimen notification_action_icon_size 0x7f030007
int dimen notification_action_text_size 0x7f030008
int dimen notification_big_circle_margin 0x7f030009
int dimen notification_content_margin_start 0x7f03000a
int dimen notification_large_icon_height 0x7f03000b
int dimen notification_large_icon_width 0x7f03000c
int dimen notification_main_column_padding_top 0x7f03000d
int dimen notification_media_narrow_margin 0x7f03000e
int dimen notification_right_icon_size 0x7f03000f
int dimen notification_right_side_padding_top 0x7f030010
int dimen notification_small_icon_background_padding 0x7f030011
int dimen notification_small_icon_size_as_large 0x7f030012
int dimen notification_subtext_size 0x7f030013
int dimen notification_top_pad 0x7f030014
int dimen notification_top_pad_large_text 0x7f030015
int drawable circular_progress_drawable 0x7f040001
int drawable circular_reminder_background 0x7f040002
int drawable floating_button_background 0x7f040003
int drawable haze_noise 0x7f040004
int drawable ic_add_skyblue 0x7f040005
int drawable ic_apps_24 0x7f040006
int drawable ic_call_answer 0x7f040007
int drawable ic_call_answer_low 0x7f040008
int drawable ic_call_answer_video 0x7f040009
int drawable ic_call_answer_video_low 0x7f04000a
int drawable ic_call_decline 0x7f04000b
int drawable ic_call_decline_low 0x7f04000c
int drawable ic_circle_white 0x7f04000d
int drawable ic_clear 0x7f04000e
int drawable ic_close 0x7f04000f
int drawable ic_close_24 0x7f040010
int drawable ic_launcher_background 0x7f040011
int drawable ic_launcher_foreground 0x7f040012
int drawable ic_lightbulb_24 0x7f040013
int drawable ic_long_press 0x7f040014
int drawable ic_map_24 0x7f040015
int drawable ic_music_note_24 0x7f040016
int drawable ic_notification 0x7f040017
int drawable ic_pause 0x7f040018
int drawable ic_play_arrow 0x7f040019
int drawable ic_radio_button_checked_sky_blue 0x7f04001a
int drawable ic_radio_button_unchecked_sky_blue 0x7f04001b
int drawable ic_record_voice_over 0x7f04001c
int drawable ic_save 0x7f04001d
int drawable ic_screen_rotation_24 0x7f04001e
int drawable ic_search_sky_blue_bold 0x7f04001f
int drawable ic_search_sky_blue_medium 0x7f040020
int drawable ic_search_sky_blue_regular 0x7f040021
int drawable ic_settings 0x7f040022
int drawable ic_share_24 0x7f040023
int drawable ic_shopping_cart_24 0x7f040024
int drawable ic_shortcut_command 0x7f040025
int drawable ic_sky_blue_arrow_right 0x7f040026
int drawable ic_sky_blue_back_arrow 0x7f040027
int drawable ic_sky_blue_check_disabled 0x7f040028
int drawable ic_sky_blue_check_enabled 0x7f040029
int drawable ic_sky_blue_more_vert 0x7f04002a
int drawable ic_stop 0x7f04002b
int drawable ic_swipe 0x7f04002c
int drawable ic_touch_app 0x7f04002d
int drawable menu_background 0x7f04002e
int drawable menu_item_background 0x7f04002f
int drawable notification_action_background 0x7f040030
int drawable notification_bg 0x7f040031
int drawable notification_bg_low 0x7f040032
int drawable notification_bg_low_normal 0x7f040033
int drawable notification_bg_low_pressed 0x7f040034
int drawable notification_bg_normal 0x7f040035
int drawable notification_bg_normal_pressed 0x7f040036
int drawable notification_icon_background 0x7f040037
int drawable notification_oversize_large_icon_bg 0x7f040038
int drawable notification_template_icon_bg 0x7f040039
int drawable notification_template_icon_low_bg 0x7f04003a
int drawable notification_tile_bg 0x7f04003b
int drawable notify_panel_notification_icon_bg 0x7f04003c
int drawable overlay_background 0x7f04003d
int drawable overlay_background_md3 0x7f04003e
int drawable template_anti_embarrassment_mode 0x7f04003f
int drawable template_screen_off_network 0x7f040040
int drawable template_screen_on_network 0x7f040041
int drawable widget_background 0x7f040042
int id accessibility_action_clickable_span 0x7f050000
int id accessibility_custom_action_0 0x7f050001
int id accessibility_custom_action_1 0x7f050002
int id accessibility_custom_action_10 0x7f050003
int id accessibility_custom_action_11 0x7f050004
int id accessibility_custom_action_12 0x7f050005
int id accessibility_custom_action_13 0x7f050006
int id accessibility_custom_action_14 0x7f050007
int id accessibility_custom_action_15 0x7f050008
int id accessibility_custom_action_16 0x7f050009
int id accessibility_custom_action_17 0x7f05000a
int id accessibility_custom_action_18 0x7f05000b
int id accessibility_custom_action_19 0x7f05000c
int id accessibility_custom_action_2 0x7f05000d
int id accessibility_custom_action_20 0x7f05000e
int id accessibility_custom_action_21 0x7f05000f
int id accessibility_custom_action_22 0x7f050010
int id accessibility_custom_action_23 0x7f050011
int id accessibility_custom_action_24 0x7f050012
int id accessibility_custom_action_25 0x7f050013
int id accessibility_custom_action_26 0x7f050014
int id accessibility_custom_action_27 0x7f050015
int id accessibility_custom_action_28 0x7f050016
int id accessibility_custom_action_29 0x7f050017
int id accessibility_custom_action_3 0x7f050018
int id accessibility_custom_action_30 0x7f050019
int id accessibility_custom_action_31 0x7f05001a
int id accessibility_custom_action_4 0x7f05001b
int id accessibility_custom_action_5 0x7f05001c
int id accessibility_custom_action_6 0x7f05001d
int id accessibility_custom_action_7 0x7f05001e
int id accessibility_custom_action_8 0x7f05001f
int id accessibility_custom_action_9 0x7f050020
int id action_clear 0x7f050021
int id action_container 0x7f050022
int id action_divider 0x7f050023
int id action_image 0x7f050024
int id action_save 0x7f050025
int id action_settings 0x7f050026
int id action_text 0x7f050027
int id actions 0x7f050028
int id androidx_compose_ui_view_composition_context 0x7f050029
int id async 0x7f05002a
int id blocking 0x7f05002b
int id chronometer 0x7f05002c
int id circular_reminder_button 0x7f05002d
int id compose_view_saveable_id_tag 0x7f05002e
int id consume_window_insets_tag 0x7f05002f
int id dialog_button 0x7f050030
int id edit_text_id 0x7f050031
int id forever 0x7f050032
int id hide_graphics_layer_in_inspector_tag 0x7f050033
int id hide_ime_id 0x7f050034
int id hide_in_inspector_tag 0x7f050035
int id icon 0x7f050036
int id icon_group 0x7f050037
int id info 0x7f050038
int id inspection_slot_table_set 0x7f050039
int id is_pooling_container_tag 0x7f05003a
int id italic 0x7f05003b
int id iv_reminder_icon 0x7f05003c
int id line1 0x7f05003d
int id line3 0x7f05003e
int id nav_controller_view_tag 0x7f05003f
int id normal 0x7f050040
int id notification_background 0x7f050041
int id notification_main_column 0x7f050042
int id notification_main_column_container 0x7f050043
int id pooling_container_listener_holder_tag 0x7f050044
int id progress_auto_dismiss 0x7f050045
int id report_drawn 0x7f050046
int id right_icon 0x7f050047
int id right_side 0x7f050048
int id tag_accessibility_actions 0x7f050049
int id tag_accessibility_clickable_spans 0x7f05004a
int id tag_accessibility_heading 0x7f05004b
int id tag_accessibility_pane_title 0x7f05004c
int id tag_compat_insets_dispatch 0x7f05004d
int id tag_on_apply_window_listener 0x7f05004e
int id tag_on_receive_content_listener 0x7f05004f
int id tag_on_receive_content_mime_types 0x7f050050
int id tag_screen_reader_focusable 0x7f050051
int id tag_state_description 0x7f050052
int id tag_system_bar_state_monitor 0x7f050053
int id tag_transition_group 0x7f050054
int id tag_unhandled_key_event_manager 0x7f050055
int id tag_unhandled_key_listeners 0x7f050056
int id tag_window_insets_animation_callback 0x7f050057
int id text 0x7f050058
int id text2 0x7f050059
int id time 0x7f05005a
int id title 0x7f05005b
int id view_tree_disjoint_parent 0x7f05005c
int id view_tree_lifecycle_owner 0x7f05005d
int id view_tree_on_back_pressed_dispatcher_owner 0x7f05005e
int id view_tree_saved_state_registry_owner 0x7f05005f
int id view_tree_view_model_store_owner 0x7f050060
int id widget_text_line1 0x7f050061
int id widget_text_line2 0x7f050062
int id wrapped_composition_tag 0x7f050063
int integer m3c_window_layout_in_display_cutout_mode 0x7f060000
int integer status_bar_notification_info_maxnum 0x7f060001
int layout custom_dialog 0x7f070000
int layout ime_base_split_test_activity 0x7f070001
int layout ime_secondary_split_test_activity 0x7f070002
int layout notification_action 0x7f070003
int layout notification_action_tombstone 0x7f070004
int layout notification_template_custom_big 0x7f070005
int layout notification_template_icon_group 0x7f070006
int layout notification_template_part_chronometer 0x7f070007
int layout notification_template_part_time 0x7f070008
int layout overlay_smart_reminder 0x7f070009
int layout widget_one_click_command 0x7f07000a
int menu advanced_recording_menu 0x7f080000
int mipmap ic_launcher 0x7f090000
int mipmap ic_launcher_round 0x7f090001
int string accessibility_service_description 0x7f0a0000
int string accessibility_service_disable 0x7f0a0001
int string accessibility_service_enable 0x7f0a0002
int string accessibility_service_go_to_settings 0x7f0a0003
int string accessibility_service_manual_title 0x7f0a0004
int string accessibility_service_name 0x7f0a0005
int string accessibility_service_system_description 0x7f0a0006
int string accessibility_service_title 0x7f0a0007
int string account_selection 0x7f0a0008
int string add_cleanup_rule 0x7f0a0009
int string add_cleanup_rule_description 0x7f0a000a
int string address_reminder_description 0x7f0a000b
int string address_reminder_message 0x7f0a000c
int string address_reminder_status_disabled 0x7f0a000d
int string address_reminder_status_enabled 0x7f0a000e
int string address_reminder_title 0x7f0a000f
int string advanced_cleanup_strategy 0x7f0a0010
int string advanced_cleanup_strategy_description 0x7f0a0011
int string advanced_memory_config 0x7f0a0012
int string airplane_mode_changed 0x7f0a0013
int string airplane_mode_changed_description 0x7f0a0014
int string ambient_display 0x7f0a0015
int string ambient_display_description 0x7f0a0016
int string androidx_startup 0x7f0a0017
int string app_detection_any_app 0x7f0a0018
int string app_detection_selected_apps 0x7f0a0019
int string app_execute_javascript 0x7f0a001a
int string app_execute_javascript_description 0x7f0a001b
int string app_execute_shell_script 0x7f0a001c
int string app_execute_shell_script_description 0x7f0a001d
int string app_force_stop_app 0x7f0a001e
int string app_force_stop_app_description 0x7f0a001f
int string app_freeze_app 0x7f0a0020
int string app_freeze_app_description 0x7f0a0021
int string app_importance_management 0x7f0a0022
int string app_importance_management_description 0x7f0a0023
int string app_interface_click 0x7f0a0024
int string app_interface_click_description 0x7f0a0025
int string app_launch_app 0x7f0a0026
int string app_launch_app_description 0x7f0a0027
int string app_lifecycle 0x7f0a0028
int string app_lifecycle_description 0x7f0a0029
int string app_link_reminder_description 0x7f0a002a
int string app_link_reminder_status_disabled 0x7f0a002b
int string app_link_reminder_status_enabled 0x7f0a002c
int string app_link_reminder_status_unconfigured 0x7f0a002d
int string app_link_reminder_title 0x7f0a002e
int string app_management 0x7f0a002f
int string app_management_description 0x7f0a0030
int string app_name 0x7f0a0031
int string app_open_website 0x7f0a0032
int string app_open_website_description 0x7f0a0033
int string app_package_management 0x7f0a0034
int string app_package_management_description 0x7f0a0035
int string app_screen_content 0x7f0a0036
int string app_screen_content_description 0x7f0a0037
int string app_selection 0x7f0a0038
int string app_state 0x7f0a0039
int string app_state_change 0x7f0a003a
int string app_state_change_description 0x7f0a003b
int string app_state_description 0x7f0a003c
int string app_tasker_locale_plugin 0x7f0a003d
int string app_tasker_locale_plugin_description 0x7f0a003e
int string app_trigger_all_apps 0x7f0a003f
int string app_trigger_any_app 0x7f0a0040
int string app_unfreeze_app 0x7f0a0041
int string app_unfreeze_app_description 0x7f0a0042
int string application_task 0x7f0a0043
int string application_task_description 0x7f0a0044
int string auto_clicker_accessibility_service_description 0x7f0a0045
int string auto_clicker_accessibility_service_name 0x7f0a0046
int string auto_rotate_disable 0x7f0a0047
int string auto_rotate_enable 0x7f0a0048
int string auto_sync_state 0x7f0a0049
int string auto_sync_state_description 0x7f0a004a
int string autofill 0x7f0a004b
int string back 0x7f0a004c
int string batch_delete_confirm 0x7f0a004d
int string batch_delete_quick_commands 0x7f0a004e
int string batch_delete_warning 0x7f0a004f
int string battery_battery_level 0x7f0a0050
int string battery_battery_level_description 0x7f0a0051
int string battery_charging_state 0x7f0a0052
int string battery_charging_state_description 0x7f0a0053
int string battery_state 0x7f0a0054
int string battery_state_description 0x7f0a0055
int string call_notification_answer_action 0x7f0a0056
int string call_notification_answer_video_action 0x7f0a0057
int string call_notification_decline_action 0x7f0a0058
int string call_notification_hang_up_action 0x7f0a0059
int string call_notification_incoming_text 0x7f0a005a
int string call_notification_ongoing_text 0x7f0a005b
int string call_notification_screening_text 0x7f0a005c
int string camera_flashlight_control 0x7f0a005d
int string camera_flashlight_control_description 0x7f0a005e
int string camera_open_last_photo 0x7f0a005f
int string camera_open_last_photo_description 0x7f0a0060
int string camera_record_video 0x7f0a0061
int string camera_record_video_description 0x7f0a0062
int string camera_screenshot 0x7f0a0063
int string camera_screenshot_description 0x7f0a0064
int string camera_take_photo 0x7f0a0065
int string camera_take_photo_description 0x7f0a0066
int string camera_task 0x7f0a0067
int string camera_task_description 0x7f0a0068
int string cancel 0x7f0a0069
int string change_map_apps 0x7f0a006a
int string change_music_apps 0x7f0a006b
int string check_text_content 0x7f0a006c
int string checkup_button 0x7f0a006d
int string clipboard_changed 0x7f0a006e
int string clipboard_changed_description 0x7f0a006f
int string close_drawer 0x7f0a0070
int string close_sheet 0x7f0a0071
int string comm_call_active 0x7f0a0072
int string comm_call_active_description 0x7f0a0073
int string comm_call_ended 0x7f0a0074
int string comm_call_ended_description 0x7f0a0075
int string comm_incoming_call 0x7f0a0076
int string comm_incoming_call_description 0x7f0a0077
int string comm_outgoing_call 0x7f0a0078
int string comm_outgoing_call_description 0x7f0a0079
int string comm_sms_received 0x7f0a007a
int string comm_sms_received_description 0x7f0a007b
int string comm_sms_sent 0x7f0a007c
int string comm_sms_sent_description 0x7f0a007d
int string communication_state 0x7f0a007e
int string communication_state_description 0x7f0a007f
int string configure_item 0x7f0a0080
int string confirm_configuration 0x7f0a0081
int string conn_bluetooth_state 0x7f0a0082
int string conn_bluetooth_state_description 0x7f0a0083
int string conn_mobile_data 0x7f0a0084
int string conn_mobile_data_description 0x7f0a0085
int string conn_wifi_network 0x7f0a0086
int string conn_wifi_network_description 0x7f0a0087
int string conn_wifi_state 0x7f0a0088
int string conn_wifi_state_description 0x7f0a0089
int string connection_state 0x7f0a008a
int string connection_state_description 0x7f0a008b
int string connectivity_airplane_mode_control 0x7f0a008c
int string connectivity_airplane_mode_control_description 0x7f0a008d
int string connectivity_bluetooth_control 0x7f0a008e
int string connectivity_bluetooth_control_description 0x7f0a008f
int string connectivity_hotspot_control 0x7f0a0090
int string connectivity_hotspot_control_description 0x7f0a0091
int string connectivity_mobile_data_control 0x7f0a0092
int string connectivity_mobile_data_control_description 0x7f0a0093
int string connectivity_network_check 0x7f0a0094
int string connectivity_network_check_description 0x7f0a0095
int string connectivity_nfc_control 0x7f0a0096
int string connectivity_nfc_control_description 0x7f0a0097
int string connectivity_send_intent 0x7f0a0098
int string connectivity_send_intent_description 0x7f0a0099
int string connectivity_task 0x7f0a009a
int string connectivity_task_description 0x7f0a009b
int string connectivity_wifi_control 0x7f0a009c
int string connectivity_wifi_control_description 0x7f0a009d
int string contact_selection 0x7f0a009e
int string custom_app_platform_config 0x7f0a009f
int string custom_shopping_platform_config 0x7f0a00a0
int string dark_theme_changed 0x7f0a00a1
int string dark_theme_changed_description 0x7f0a00a2
int string datetime_alarm 0x7f0a00a3
int string datetime_alarm_description 0x7f0a00a4
int string datetime_stopwatch 0x7f0a00a5
int string datetime_stopwatch_description 0x7f0a00a6
int string datetime_task 0x7f0a00a7
int string datetime_task_description 0x7f0a00a8
int string datetime_voice_time_announcement 0x7f0a00a9
int string datetime_voice_time_announcement_description 0x7f0a00aa
int string default_error_message 0x7f0a00ab
int string default_keyboard 0x7f0a00ac
int string default_keyboard_description 0x7f0a00ad
int string default_popup_window_title 0x7f0a00ae
int string delete 0x7f0a00af
int string delete_operation_irreversible 0x7f0a00b0
int string delete_quick_command 0x7f0a00b1
int string delete_quick_command_confirm 0x7f0a00b2
int string delete_quick_command_warning 0x7f0a00b3
int string demo_mode 0x7f0a00b4
int string demo_mode_description 0x7f0a00b5
int string detailed_configuration 0x7f0a00b6
int string device_action_share_text 0x7f0a00b7
int string device_action_share_text_description 0x7f0a00b8
int string device_action_task 0x7f0a00b9
int string device_action_task_description 0x7f0a00ba
int string device_boot_completed 0x7f0a00bb
int string device_boot_completed_description 0x7f0a00bc
int string device_clipboard_changed 0x7f0a00bd
int string device_clipboard_changed_description 0x7f0a00be
int string device_event 0x7f0a00bf
int string device_event_description 0x7f0a00c0
int string device_gps_state 0x7f0a00c1
int string device_gps_state_description 0x7f0a00c2
int string device_logcat_message 0x7f0a00c3
int string device_logcat_message_description 0x7f0a00c4
int string device_settings 0x7f0a00c5
int string device_settings_accessibility_service 0x7f0a00c6
int string device_settings_accessibility_service_description 0x7f0a00c7
int string device_settings_auto_rotate 0x7f0a00c8
int string device_settings_auto_rotate_description 0x7f0a00c9
int string device_settings_description 0x7f0a00ca
int string device_settings_display_density 0x7f0a00cb
int string device_settings_display_density_description 0x7f0a00cc
int string device_settings_driving_mode 0x7f0a00cd
int string device_settings_driving_mode_description 0x7f0a00ce
int string device_settings_enter_screensaver 0x7f0a00cf
int string device_settings_enter_screensaver_description 0x7f0a00d0
int string device_settings_font_size 0x7f0a00d1
int string device_settings_font_size_description 0x7f0a00d2
int string device_settings_immersive_mode 0x7f0a00d3
int string device_settings_immersive_mode_description 0x7f0a00d4
int string device_settings_invert_colors 0x7f0a00d5
int string device_settings_invert_colors_description 0x7f0a00d6
int string device_settings_keyboard_hint 0x7f0a00d7
int string device_settings_keyboard_hint_description 0x7f0a00d8
int string dialog_content 0x7f0a00d9
int string dialog_content_placeholder 0x7f0a00da
int string dialog_settings 0x7f0a00db
int string dialog_title 0x7f0a00dc
int string dialog_title_placeholder 0x7f0a00dd
int string digital_assistant 0x7f0a00de
int string digital_assistant_description 0x7f0a00df
int string disable 0x7f0a00e0
int string dock_state 0x7f0a00e1
int string dock_state_description 0x7f0a00e2
int string driving_mode 0x7f0a00e3
int string driving_mode_description 0x7f0a00e4
int string dropdown_menu 0x7f0a00e5
int string edit_abort_condition 0x7f0a00e6
int string edit_abort_condition_description 0x7f0a00e7
int string edit_condition 0x7f0a00e8
int string edit_condition_description 0x7f0a00e9
int string edit_task 0x7f0a00ea
int string edit_task_description 0x7f0a00eb
int string enable 0x7f0a00ec
int string executing_quick_command 0x7f0a00ed
int string execution_mode 0x7f0a00ee
int string experimental_features 0x7f0a00ef
int string experimental_features_description 0x7f0a00f0
int string experimental_features_enabled 0x7f0a00f1
int string file_file_operation 0x7f0a00f2
int string file_file_operation_description 0x7f0a00f3
int string file_open_file 0x7f0a00f4
int string file_open_file_description 0x7f0a00f5
int string file_operation_task 0x7f0a00f6
int string file_operation_task_description 0x7f0a00f7
int string file_write_file 0x7f0a00f8
int string file_write_file_description 0x7f0a00f9
int string flashlight_reminder_description 0x7f0a00fa
int string flashlight_reminder_status_disabled 0x7f0a00fb
int string flashlight_reminder_status_enabled 0x7f0a00fc
int string flashlight_reminder_status_unconfigured 0x7f0a00fd
int string flashlight_reminder_title 0x7f0a00fe
int string font_size 0x7f0a00ff
int string font_size_description 0x7f0a0100
int string font_size_percentage 0x7f0a0101
int string font_size_settings 0x7f0a0102
int string font_weight_bold 0x7f0a0103
int string font_weight_medium 0x7f0a0104
int string font_weight_regular 0x7f0a0105
int string font_weight_selection_title 0x7f0a0106
int string gesture_recognition_accessibility_service_description 0x7f0a0107
int string gesture_recognition_accessibility_service_name 0x7f0a0108
int string gesture_recording_edit 0x7f0a0109
int string gesture_recording_edit_description 0x7f0a010a
int string go_to_settings 0x7f0a010b
int string gps_state 0x7f0a010c
int string gps_state_description 0x7f0a010d
int string icon_weight_bold 0x7f0a010e
int string icon_weight_medium 0x7f0a010f
int string icon_weight_regular 0x7f0a0110
int string icon_weight_selection_title 0x7f0a0111
int string in_progress 0x7f0a0112
int string indeterminate 0x7f0a0113
int string info_message_ringtone 0x7f0a0114
int string info_message_ringtone_description 0x7f0a0115
int string info_send_email 0x7f0a0116
int string info_send_email_description 0x7f0a0117
int string info_send_sms 0x7f0a0118
int string info_send_sms_description 0x7f0a0119
int string info_show_dialog 0x7f0a011a
int string info_show_dialog_description 0x7f0a011b
int string info_show_toast 0x7f0a011c
int string info_show_toast_description 0x7f0a011d
int string information_task 0x7f0a011e
int string information_task_description 0x7f0a011f
int string intelligent_link_recognition_description 0x7f0a0120
int string intelligent_link_recognition_enabled 0x7f0a0121
int string intelligent_link_recognition_help 0x7f0a0122
int string intelligent_link_recognition_title 0x7f0a0123
int string intent_received 0x7f0a0124
int string intent_received_description 0x7f0a0125
int string interface_click 0x7f0a0126
int string interface_click_description 0x7f0a0127
int string interface_interaction_accessibility_service_description 0x7f0a0128
int string interface_interaction_accessibility_service_name 0x7f0a0129
int string interface_interaction_service_not_enabled 0x7f0a012a
int string invalid_command 0x7f0a012b
int string invert_colors 0x7f0a012c
int string invert_colors_description 0x7f0a012d
int string invert_colors_off 0x7f0a012e
int string invert_colors_on 0x7f0a012f
int string invert_colors_operation 0x7f0a0130
int string keyboard_hint 0x7f0a0131
int string keyboard_hint_description 0x7f0a0132
int string language_chinese 0x7f0a0133
int string language_english 0x7f0a0134
int string language_selection_title 0x7f0a0135
int string language_settings 0x7f0a0136
int string language_settings_description 0x7f0a0137
int string language_system_default 0x7f0a0138
int string lifecycle 0x7f0a0139
int string lifecycle_description 0x7f0a013a
int string location_force_location_update 0x7f0a013b
int string location_force_location_update_description 0x7f0a013c
int string location_get_location 0x7f0a013d
int string location_get_location_description 0x7f0a013e
int string location_set_location_update_frequency 0x7f0a013f
int string location_set_location_update_frequency_description 0x7f0a0140
int string location_share_location 0x7f0a0141
int string location_share_location_description 0x7f0a0142
int string location_task 0x7f0a0143
int string location_task_description 0x7f0a0144
int string location_toggle_location_service 0x7f0a0145
int string location_toggle_location_service_description 0x7f0a0146
int string logcat_message 0x7f0a0147
int string logcat_message_description 0x7f0a0148
int string login_attempt_failed 0x7f0a0149
int string login_attempt_failed_description 0x7f0a014a
int string m3c_bottom_sheet_collapse_description 0x7f0a014b
int string m3c_bottom_sheet_dismiss_description 0x7f0a014c
int string m3c_bottom_sheet_drag_handle_description 0x7f0a014d
int string m3c_bottom_sheet_expand_description 0x7f0a014e
int string m3c_bottom_sheet_pane_title 0x7f0a014f
int string m3c_date_input_headline 0x7f0a0150
int string m3c_date_input_headline_description 0x7f0a0151
int string m3c_date_input_invalid_for_pattern 0x7f0a0152
int string m3c_date_input_invalid_not_allowed 0x7f0a0153
int string m3c_date_input_invalid_year_range 0x7f0a0154
int string m3c_date_input_label 0x7f0a0155
int string m3c_date_input_no_input_description 0x7f0a0156
int string m3c_date_input_title 0x7f0a0157
int string m3c_date_picker_headline 0x7f0a0158
int string m3c_date_picker_headline_description 0x7f0a0159
int string m3c_date_picker_navigate_to_year_description 0x7f0a015a
int string m3c_date_picker_no_selection_description 0x7f0a015b
int string m3c_date_picker_scroll_to_earlier_years 0x7f0a015c
int string m3c_date_picker_scroll_to_later_years 0x7f0a015d
int string m3c_date_picker_switch_to_calendar_mode 0x7f0a015e
int string m3c_date_picker_switch_to_day_selection 0x7f0a015f
int string m3c_date_picker_switch_to_input_mode 0x7f0a0160
int string m3c_date_picker_switch_to_next_month 0x7f0a0161
int string m3c_date_picker_switch_to_previous_month 0x7f0a0162
int string m3c_date_picker_switch_to_year_selection 0x7f0a0163
int string m3c_date_picker_title 0x7f0a0164
int string m3c_date_picker_today_description 0x7f0a0165
int string m3c_date_picker_year_picker_pane_title 0x7f0a0166
int string m3c_date_range_input_invalid_range_input 0x7f0a0167
int string m3c_date_range_input_title 0x7f0a0168
int string m3c_date_range_picker_day_in_range 0x7f0a0169
int string m3c_date_range_picker_end_headline 0x7f0a016a
int string m3c_date_range_picker_scroll_to_next_month 0x7f0a016b
int string m3c_date_range_picker_scroll_to_previous_month 0x7f0a016c
int string m3c_date_range_picker_start_headline 0x7f0a016d
int string m3c_date_range_picker_title 0x7f0a016e
int string m3c_dialog 0x7f0a016f
int string m3c_dropdown_menu_collapsed 0x7f0a0170
int string m3c_dropdown_menu_expanded 0x7f0a0171
int string m3c_dropdown_menu_toggle 0x7f0a0172
int string m3c_search_bar_search 0x7f0a0173
int string m3c_snackbar_dismiss 0x7f0a0174
int string m3c_suggestions_available 0x7f0a0175
int string m3c_time_picker_am 0x7f0a0176
int string m3c_time_picker_hour 0x7f0a0177
int string m3c_time_picker_hour_24h_suffix 0x7f0a0178
int string m3c_time_picker_hour_selection 0x7f0a0179
int string m3c_time_picker_hour_suffix 0x7f0a017a
int string m3c_time_picker_hour_text_field 0x7f0a017b
int string m3c_time_picker_minute 0x7f0a017c
int string m3c_time_picker_minute_selection 0x7f0a017d
int string m3c_time_picker_minute_suffix 0x7f0a017e
int string m3c_time_picker_minute_text_field 0x7f0a017f
int string m3c_time_picker_period_toggle_description 0x7f0a0180
int string m3c_time_picker_pm 0x7f0a0181
int string m3c_tooltip_long_press_label 0x7f0a0182
int string m3c_tooltip_pane_description 0x7f0a0183
int string manual_dynamic_shortcut 0x7f0a0184
int string manual_dynamic_shortcut_description 0x7f0a0185
int string manual_fingerprint_gesture 0x7f0a0186
int string manual_fingerprint_gesture_description 0x7f0a0187
int string manual_home_button_long_press 0x7f0a0188
int string manual_home_button_long_press_description 0x7f0a0189
int string manual_media_key_press 0x7f0a018a
int string manual_media_key_press_description 0x7f0a018b
int string manual_static_shortcut 0x7f0a018c
int string manual_static_shortcut_description 0x7f0a018d
int string manual_trigger 0x7f0a018e
int string manual_trigger_description 0x7f0a018f
int string manual_volume_key_press 0x7f0a0190
int string manual_volume_key_press_description 0x7f0a0191
int string manual_widget_update 0x7f0a0192
int string manual_widget_update_description 0x7f0a0193
int string match_options 0x7f0a0194
int string media_microphone_recording 0x7f0a0195
int string media_microphone_recording_description 0x7f0a0196
int string media_multimedia_control 0x7f0a0197
int string media_multimedia_control_description 0x7f0a0198
int string media_play_stop_sound 0x7f0a0199
int string media_play_stop_sound_description 0x7f0a019a
int string media_task 0x7f0a019b
int string media_task_description 0x7f0a019c
int string memory_learning_data 0x7f0a019d
int string music_app_reminder_description 0x7f0a019e
int string music_app_reminder_status_configured 0x7f0a019f
int string music_app_reminder_status_disabled 0x7f0a01a0
int string music_app_reminder_status_enabled 0x7f0a01a1
int string music_app_reminder_status_unconfigured 0x7f0a01a2
int string music_app_reminder_title 0x7f0a01a3
int string music_playback_state 0x7f0a01a4
int string music_playback_state_description 0x7f0a01a5
int string nav_command_templates 0x7f0a01a6
int string nav_global_settings 0x7f0a01a7
int string nav_phone_checkup 0x7f0a01a8
int string nav_quick_commands 0x7f0a01a9
int string nav_smart_reminders 0x7f0a01aa
int string navigation_menu 0x7f0a01ab
int string new_app_reminder_description 0x7f0a01ac
int string new_app_reminder_status_disabled 0x7f0a01ad
int string new_app_reminder_status_enabled 0x7f0a01ae
int string new_app_reminder_status_unconfigured 0x7f0a01af
int string new_app_reminder_title 0x7f0a01b0
int string no_quick_commands 0x7f0a01b1
int string no_search_results 0x7f0a01b2
int string no_template_search_results 0x7f0a01b3
int string no_templates 0x7f0a01b4
int string not_selected 0x7f0a01b5
int string notification_cancel_notification 0x7f0a01b6
int string notification_cancel_notification_description 0x7f0a01b7
int string notification_event 0x7f0a01b8
int string notification_event_description 0x7f0a01b9
int string notification_listener_service_description 0x7f0a01ba
int string notification_listener_service_name 0x7f0a01bb
int string notification_show_notification 0x7f0a01bc
int string notification_show_notification_description 0x7f0a01bd
int string notification_task 0x7f0a01be
int string notification_task_description 0x7f0a01bf
int string optimization_complete 0x7f0a01c0
int string optimization_failed 0x7f0a01c1
int string optimize_button 0x7f0a01c2
int string optimizing 0x7f0a01c3
int string package_management 0x7f0a01c4
int string package_management_description 0x7f0a01c5
int string package_name 0x7f0a01c6
int string phone_answer_call 0x7f0a01c7
int string phone_answer_call_description 0x7f0a01c8
int string phone_checkup_title 0x7f0a01c9
int string phone_clear_call_log 0x7f0a01ca
int string phone_clear_call_log_description 0x7f0a01cb
int string phone_make_call 0x7f0a01cc
int string phone_make_call_description 0x7f0a01cd
int string phone_open_call_log 0x7f0a01ce
int string phone_open_call_log_description 0x7f0a01cf
int string phone_reject_call 0x7f0a01d0
int string phone_reject_call_description 0x7f0a01d1
int string phone_ringtone_settings 0x7f0a01d2
int string phone_ringtone_settings_description 0x7f0a01d3
int string phone_status_excellent 0x7f0a01d4
int string phone_status_good 0x7f0a01d5
int string phone_status_poor 0x7f0a01d6
int string phone_task 0x7f0a01d7
int string phone_task_description 0x7f0a01d8
int string power_save_mode 0x7f0a01d9
int string power_save_mode_description 0x7f0a01da
int string quick_command_aborted 0x7f0a01db
int string quick_command_completed 0x7f0a01dc
int string quick_command_edit 0x7f0a01dd
int string quick_command_form 0x7f0a01de
int string quick_command_new 0x7f0a01df
int string quick_command_not_found 0x7f0a01e0
int string quick_command_not_found_simple 0x7f0a01e1
int string quick_commands_title 0x7f0a01e2
int string range_end 0x7f0a01e3
int string range_start 0x7f0a01e4
int string ringer_mode_changed 0x7f0a01e5
int string ringer_mode_changed_description 0x7f0a01e6
int string ringtone_selection 0x7f0a01e7
int string running_apps_count 0x7f0a01e8
int string save 0x7f0a01e9
int string screen_brightness_control 0x7f0a01ea
int string screen_brightness_control_description 0x7f0a01eb
int string screen_content 0x7f0a01ec
int string screen_content_description 0x7f0a01ed
int string screen_content_text 0x7f0a01ee
int string screen_content_text_label 0x7f0a01ef
int string screen_content_text_placeholder 0x7f0a01f0
int string screen_control_task 0x7f0a01f1
int string screen_control_task_description 0x7f0a01f2
int string screen_event_auto_rotate_disabled 0x7f0a01f3
int string screen_event_auto_rotate_enabled 0x7f0a01f4
int string screen_event_off 0x7f0a01f5
int string screen_event_on 0x7f0a01f6
int string screen_event_unlocked 0x7f0a01f7
int string screen_keep_device_awake 0x7f0a01f8
int string screen_keep_device_awake_description 0x7f0a01f9
int string screen_rotation_reminder_description 0x7f0a01fa
int string screen_rotation_reminder_status_disabled 0x7f0a01fb
int string screen_rotation_reminder_status_enabled 0x7f0a01fc
int string screen_rotation_reminder_status_unconfigured 0x7f0a01fd
int string screen_rotation_reminder_title 0x7f0a01fe
int string screen_state 0x7f0a01ff
int string screen_state_description 0x7f0a0200
int string screen_text_check_failed 0x7f0a0201
int string search_apps 0x7f0a0202
int string search_field_icon_weight 0x7f0a0203
int string search_field_icon_weight_description 0x7f0a0204
int string search_field_placeholder_font_weight 0x7f0a0205
int string search_field_placeholder_font_weight_description 0x7f0a0206
int string search_field_settings 0x7f0a0207
int string search_quick_commands 0x7f0a0208
int string search_smart_reminders 0x7f0a0209
int string search_templates 0x7f0a020a
int string select_map_apps 0x7f0a020b
int string select_music_apps 0x7f0a020c
int string selected 0x7f0a020d
int string sensor_activity_recognition 0x7f0a020e
int string sensor_activity_recognition_description 0x7f0a020f
int string sensor_flip_sensor 0x7f0a0210
int string sensor_flip_sensor_description 0x7f0a0211
int string sensor_light_sensor 0x7f0a0212
int string sensor_light_sensor_description 0x7f0a0213
int string sensor_orientation_sensor 0x7f0a0214
int string sensor_orientation_sensor_description 0x7f0a0215
int string sensor_proximity_sensor 0x7f0a0216
int string sensor_proximity_sensor_description 0x7f0a0217
int string sensor_shake_sensor 0x7f0a0218
int string sensor_shake_sensor_description 0x7f0a0219
int string sensor_sleep_sensor 0x7f0a021a
int string sensor_sleep_sensor_description 0x7f0a021b
int string sensor_state 0x7f0a021c
int string sensor_state_description 0x7f0a021d
int string share_target_selection 0x7f0a021e
int string share_text 0x7f0a021f
int string share_text_description 0x7f0a0220
int string share_url_reminder_description 0x7f0a0221
int string share_url_reminder_status_disabled 0x7f0a0222
int string share_url_reminder_status_enabled 0x7f0a0223
int string share_url_reminder_status_unconfigured 0x7f0a0224
int string share_url_reminder_title 0x7f0a0225
int string shell_script 0x7f0a0226
int string shell_script_placeholder 0x7f0a0227
int string shell_script_placeholder_command 0x7f0a0228
int string shell_script_settings 0x7f0a0229
int string shizuku_category_app_management 0x7f0a022a
int string shizuku_category_key_simulation 0x7f0a022b
int string shizuku_category_network 0x7f0a022c
int string shizuku_category_screen_operation 0x7f0a022d
int string shizuku_category_system 0x7f0a022e
int string shizuku_install_guide 0x7f0a022f
int string shizuku_not_installed 0x7f0a0230
int string shizuku_not_running 0x7f0a0231
int string shizuku_permission_required 0x7f0a0232
int string shopping_app_reminder_description 0x7f0a0233
int string shopping_app_reminder_status_disabled 0x7f0a0234
int string shopping_app_reminder_status_enabled 0x7f0a0235
int string shopping_app_reminder_status_unconfigured 0x7f0a0236
int string shopping_app_reminder_title 0x7f0a0237
int string shortcut_1_long_label 0x7f0a0238
int string shortcut_1_short_label 0x7f0a0239
int string shortcut_2_long_label 0x7f0a023a
int string shortcut_2_short_label 0x7f0a023b
int string shortcut_3_long_label 0x7f0a023c
int string shortcut_3_short_label 0x7f0a023d
int string shortcut_4_long_label 0x7f0a023e
int string shortcut_4_short_label 0x7f0a023f
int string shortcut_not_configured 0x7f0a0240
int string sim_card_state 0x7f0a0241
int string sim_card_state_description 0x7f0a0242
int string smart_reminder_change_app 0x7f0a0243
int string smart_reminder_config_error 0x7f0a0244
int string smart_reminder_config_not_found 0x7f0a0245
int string smart_reminder_configure 0x7f0a0246
int string smart_reminder_configured 0x7f0a0247
int string smart_reminder_detail_config 0x7f0a0248
int string smart_reminder_detail_settings 0x7f0a0249
int string smart_reminder_needs_configuration 0x7f0a024a
int string smart_reminder_ready_to_use 0x7f0a024b
int string smart_reminder_select_app 0x7f0a024c
int string smart_reminder_selected_apps_count 0x7f0a024d
int string smart_reminder_status_label 0x7f0a024e
int string smart_reminder_tap_to_setup 0x7f0a024f
int string smart_reminder_type_not_found 0x7f0a0250
int string smart_reminders_description 0x7f0a0251
int string smart_reminders_title 0x7f0a0252
int string snackbar_pane_title 0x7f0a0253
int string state_change 0x7f0a0254
int string state_change_description 0x7f0a0255
int string state_empty 0x7f0a0256
int string state_off 0x7f0a0257
int string state_on 0x7f0a0258
int string status_bar_notification_info_overflow 0x7f0a0259
int string stopwatch_selection 0x7f0a025a
int string storage_permission_description 0x7f0a025b
int string storage_permission_required 0x7f0a025c
int string sun_event_sunrise 0x7f0a025d
int string sun_event_sunset 0x7f0a025e
int string switch_operation_off 0x7f0a025f
int string switch_operation_on 0x7f0a0260
int string switch_operation_toggle 0x7f0a0261
int string switch_role 0x7f0a0262
int string system_operation_accessibility_service_description 0x7f0a0263
int string system_operation_accessibility_service_name 0x7f0a0264
int string system_setting_changed 0x7f0a0265
int string system_setting_changed_description 0x7f0a0266
int string system_settings 0x7f0a0267
int string system_settings_description 0x7f0a0268
int string tab 0x7f0a0269
int string tap_to_select_map_apps 0x7f0a026a
int string tap_to_select_music_apps 0x7f0a026b
int string task_alarm_reminder 0x7f0a026c
int string task_alarm_reminder_description 0x7f0a026d
int string task_app_management 0x7f0a026e
int string task_app_management_description 0x7f0a026f
int string task_device_settings 0x7f0a0270
int string task_device_settings_description 0x7f0a0271
int string task_file_operation 0x7f0a0272
int string task_file_operation_description 0x7f0a0273
int string task_location 0x7f0a0274
int string task_location_description 0x7f0a0275
int string task_log 0x7f0a0276
int string task_log_description 0x7f0a0277
int string task_log_task 0x7f0a0278
int string task_log_task_description 0x7f0a0279
int string task_media_task 0x7f0a027a
int string task_media_task_description 0x7f0a027b
int string task_network 0x7f0a027c
int string task_network_description 0x7f0a027d
int string task_phone 0x7f0a027e
int string task_phone_description 0x7f0a027f
int string task_phone_task 0x7f0a0280
int string task_phone_task_description 0x7f0a0281
int string task_screen_control 0x7f0a0282
int string task_screen_control_description 0x7f0a0283
int string task_screen_control_task 0x7f0a0284
int string task_screen_control_task_description 0x7f0a0285
int string tasker_locale_plugin 0x7f0a0286
int string tasker_locale_plugin_description 0x7f0a0287
int string tasks_count 0x7f0a0288
int string template_anti_embarrassment_mode_desc 0x7f0a0289
int string template_anti_embarrassment_mode_title 0x7f0a028a
int string template_auto_brightness_desc 0x7f0a028b
int string template_auto_brightness_title 0x7f0a028c
int string template_battery_saver_desc 0x7f0a028d
int string template_battery_saver_title 0x7f0a028e
int string template_category_automation 0x7f0a028f
int string template_category_display 0x7f0a0290
int string template_category_network 0x7f0a0291
int string template_category_power 0x7f0a0292
int string template_category_system 0x7f0a0293
int string template_do_not_disturb_desc 0x7f0a0294
int string template_do_not_disturb_title 0x7f0a0295
int string template_percent 0x7f0a0296
int string template_screen_off_network_desc 0x7f0a0297
int string template_screen_off_network_title 0x7f0a0298
int string template_screen_on_network_desc 0x7f0a0299
int string template_screen_on_network_title 0x7f0a029a
int string text_content_label 0x7f0a029b
int string text_content_placeholder 0x7f0a029c
int string time_based 0x7f0a029d
int string time_based_description 0x7f0a029e
int string time_condition_delayed_trigger 0x7f0a029f
int string time_condition_delayed_trigger_description 0x7f0a02a0
int string time_condition_periodic_time 0x7f0a02a1
int string time_condition_periodic_time_description 0x7f0a02a2
int string time_condition_periodic_trigger 0x7f0a02a3
int string time_condition_scheduled_time 0x7f0a02a4
int string time_condition_scheduled_time_description 0x7f0a02a5
int string time_condition_stopwatch 0x7f0a02a6
int string time_condition_stopwatch_description 0x7f0a02a7
int string time_condition_sun_event 0x7f0a02a8
int string time_condition_sun_event_description 0x7f0a02a9
int string time_condition_time_period 0x7f0a02aa
int string time_condition_time_period_description 0x7f0a02ab
int string toggle 0x7f0a02ac
int string tooltip_description 0x7f0a02ad
int string tooltip_label 0x7f0a02ae
int string trigger_mode 0x7f0a02af
int string unified_configuration 0x7f0a02b0
int string update_now 0x7f0a02b1
int string usage_stats_permission_description 0x7f0a02b2
int string usage_stats_permission_required 0x7f0a02b3
int string volume_changed 0x7f0a02b4
int string volume_changed_description 0x7f0a02b5
int string volume_do_not_disturb 0x7f0a02b6
int string volume_do_not_disturb_description 0x7f0a02b7
int string volume_speakerphone_control 0x7f0a02b8
int string volume_speakerphone_control_description 0x7f0a02b9
int string volume_task 0x7f0a02ba
int string volume_task_description 0x7f0a02bb
int string volume_vibration_mode 0x7f0a02bc
int string volume_vibration_mode_description 0x7f0a02bd
int string volume_volume_adjust 0x7f0a02be
int string volume_volume_adjust_description 0x7f0a02bf
int string volume_volume_change 0x7f0a02c0
int string volume_volume_change_description 0x7f0a02c1
int string volume_volume_popup 0x7f0a02c2
int string volume_volume_popup_description 0x7f0a02c3
int string widget_1_label 0x7f0a02c4
int string widget_2_label 0x7f0a02c5
int string widget_3_label 0x7f0a02c6
int string widget_4_label 0x7f0a02c7
int string widget_update_completed 0x7f0a02c8
int string widget_update_enabled 0x7f0a02c9
int string widget_update_enabled_description 0x7f0a02ca
int string widget_update_interval 0x7f0a02cb
int string widget_update_interval_error 0x7f0a02cc
int string widget_update_interval_hint 0x7f0a02cd
int string widget_update_settings 0x7f0a02ce
int style DialogWindowTheme 0x7f0b0000
int style EdgeToEdgeFloatingDialogTheme 0x7f0b0001
int style EdgeToEdgeFloatingDialogWindowTheme 0x7f0b0002
int style FloatingDialogTheme 0x7f0b0003
int style FloatingDialogWindowTheme 0x7f0b0004
int style TextAppearance_Compat_Notification 0x7f0b0005
int style TextAppearance_Compat_Notification_Info 0x7f0b0006
int style TextAppearance_Compat_Notification_Line2 0x7f0b0007
int style TextAppearance_Compat_Notification_Time 0x7f0b0008
int style TextAppearance_Compat_Notification_Title 0x7f0b0009
int style Theme_QuickCommands 0x7f0b000a
int style Theme_QuickCommands_NoActionBar 0x7f0b000b
int style Widget_Compat_NotificationActionContainer 0x7f0b000c
int style Widget_Compat_NotificationActionText 0x7f0b000d
int[] styleable ActivityNavigator { 0x01010003, 0x7f010000, 0x7f010003, 0x7f010004, 0x7f010025 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable Capability { 0x7f010020, 0x7f010023 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f010001, 0x7f010015 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable FontFamily { 0x7f010009, 0x7f01000a, 0x7f01000b, 0x7f01000c, 0x7f01000d, 0x7f01000e, 0x7f01000f, 0x7f010010 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f010008, 0x7f010011, 0x7f010012, 0x7f010013, 0x7f010026 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable NavAction { 0x010100d0, 0x7f010005, 0x7f010006, 0x7f010007, 0x7f010016, 0x7f01001b, 0x7f01001c, 0x7f01001d, 0x7f01001e, 0x7f01001f, 0x7f010021 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f010002, 0x7f01001a }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f010000, 0x7f010017, 0x7f010027 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f010024 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f010018 }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f010014 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f010022 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int xml accessibility_service_config 0x7f0d0000
int xml auto_clicker_accessibility_service_config 0x7f0d0001
int xml backup_rules 0x7f0d0002
int xml data_extraction_rules 0x7f0d0003
int xml device_admin 0x7f0d0004
int xml file_provider_paths 0x7f0d0005
int xml gesture_recognition_accessibility_service_config 0x7f0d0006
int xml interface_interaction_accessibility_service_config 0x7f0d0007
int xml one_click_command_widget_1_info 0x7f0d0008
int xml one_click_command_widget_2_info 0x7f0d0009
int xml one_click_command_widget_3_info 0x7f0d000a
int xml one_click_command_widget_4_info 0x7f0d000b
int xml shortcuts 0x7f0d000c
int xml system_operation_accessibility_service_config 0x7f0d000d
