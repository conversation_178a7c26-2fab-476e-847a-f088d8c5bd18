package com.weinuo.quickcommands.ui.screens.oceanblue

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.AppRepository
import com.weinuo.quickcommands.data.PhoneCheckupRepository
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.execution.SharedExecutionHandler
import com.weinuo.quickcommands.ui.components.ShizukuTipCardWithPermission
import com.weinuo.quickcommands.ui.components.WaterBallComponent
import com.weinuo.quickcommands.ui.components.StaticWaterBallComponent
import com.weinuo.quickcommands.ui.components.ArcAnimationType
import com.weinuo.quickcommands.ui.components.ParticleAnimationState
import com.weinuo.quickcommands.ui.components.layered.LayeredTopAppBar
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.viewmodel.PhoneCheckupViewModel
import com.weinuo.quickcommands.viewmodel.CheckupButtonState

/**
 * 海洋蓝主题的手机体检界面
 *
 * 特点：
 * - 分层设计风格
 * - 使用阴影表现层次
 * - 清晰的组件边界
 * - 传统的Material Design布局
 * - 使用LayeredTopAppBar保持与其他界面一致
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OceanBluePhoneCheckupScreen(
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current

    // 创建依赖项
    val appRepository = remember { AppRepository(context) }
    val sharedExecutionHandler = remember { SharedExecutionHandler(context) }
    val repository = remember { PhoneCheckupRepository(context, appRepository, sharedExecutionHandler) }
    val viewModel = remember { PhoneCheckupViewModel(repository, context) }

    // 收集状态
    val healthScore by viewModel.healthScore.collectAsStateWithLifecycle()
    val runningAppsCount by viewModel.runningAppsCount.collectAsStateWithLifecycle()
    val buttonState by viewModel.buttonState.collectAsStateWithLifecycle()
    val isOptimizing by viewModel.isOptimizing.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val lastOptimizeResult by viewModel.lastOptimizeResult.collectAsStateWithLifecycle()
    val errorMessage by viewModel.errorMessage.collectAsStateWithLifecycle()
    val showShizukuTip by viewModel.showShizukuTipCard.collectAsStateWithLifecycle()
    val particleAnimationState by viewModel.particleAnimationState.collectAsStateWithLifecycle()
    val shouldStartFirstTimeContraction by viewModel.shouldStartFirstTimeContraction.collectAsStateWithLifecycle()

    // 全局设置
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 响应式状态文字
    val healthStatusDescription by viewModel.healthStatusDescription.collectAsStateWithLifecycle()
    val secondaryStatusText by viewModel.secondaryStatusText.collectAsStateWithLifecycle()

    // 注意：不需要手动刷新，ViewModel会在初始化时自动开始体检

    // 获取屏幕配置信息
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    // 计算自适应尺寸
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp

    // 根据屏幕高度动态计算水球大小
    val waterBallSize = remember(screenHeight) {
        when {
            screenHeight < 600.dp -> minOf(screenWidth * 0.35f, 140.dp)
            screenHeight < 700.dp -> minOf(screenWidth * 0.4f, 160.dp)
            screenHeight < 800.dp -> minOf(screenWidth * 0.45f, 180.dp)
            else -> minOf(screenWidth * 0.5f, 200.dp)
        }
    }

    // 根据屏幕高度动态计算间距和内边距
    val cardPadding = if (screenHeight < 600.dp) 16.dp else 20.dp
    val sectionSpacing = if (screenHeight < 600.dp) 16.dp else 20.dp
    val waterBallPadding = if (screenHeight < 600.dp) 16.dp else 24.dp

    // 滚动行为 - 支持可折叠标题栏
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior()

    Scaffold(
        modifier = modifier,
        topBar = {
            LayeredTopAppBar(
                config = TopAppBarConfig(
                    title = stringResource(R.string.phone_checkup_title),
                    style = TopAppBarStyle.STANDARD,
                    collapsible = true,
                    scrollBehavior = scrollBehavior,
                    windowInsets = WindowInsets.statusBars
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 20.dp)
                .nestedScroll(scrollBehavior.nestedScrollConnection),
            horizontalAlignment = Alignment.CenterHorizontally,
            state = rememberLazyListState(),
            contentPadding = PaddingValues(vertical = 16.dp)
        ) {

        // Shizuku提示卡片
        item {
            ShizukuTipCardWithPermission(
                visible = showShizukuTip,
                onDismiss = { viewModel.dismissShizukuTipCard() },
                modifier = Modifier.padding(bottom = if (showShizukuTip) 16.dp else 0.dp)
            )
        }

        // 水球组件 - 直接显示，无背景
        item {
            when (buttonState) {
                CheckupButtonState.FIRST_TIME, CheckupButtonState.PERMISSION_NEEDED -> {
                    // 首次进入状态和权限需要状态使用静态水球
                    StaticWaterBallComponent(
                        size = waterBallSize,
                        shouldStartContraction = shouldStartFirstTimeContraction, // 监听ViewModel的触发信号
                        onContractionComplete = {
                            // 收缩动画完成后，切换到动态水球
                            // 这里不需要做任何操作，因为状态变化会自动切换到WaterBallComponent
                        },
                        modifier = Modifier.padding(waterBallPadding).padding(bottom = sectionSpacing)
                    )
                }
                else -> {
                    // 所有其他状态使用统一的动态水球，避免重新创建导致粒子重新初始化
                    WaterBallComponent(
                        score = healthScore,
                        size = waterBallSize,
                        isAnimating = when (buttonState) {
                            CheckupButtonState.MANUAL_CHECKING,
                            CheckupButtonState.OPTIMIZING -> true
                            else -> !isLoading
                        },
                        arcType = when {
                            // 旋转弧线在处理状态和等待展示状态时显示
                            particleAnimationState == ParticleAnimationState.PROCESSING ||
                            particleAnimationState == ParticleAnimationState.WAITING_DISPLAY -> ArcAnimationType.ROTATING
                            // 静态进度弧线在静止状态时显示（除了首次体检状态和权限状态）
                            particleAnimationState == ParticleAnimationState.NORMAL &&
                            buttonState !in listOf(
                                CheckupButtonState.FIRST_TIME,
                                CheckupButtonState.PERMISSION_NEEDED
                            ) -> ArcAnimationType.STATIC_PROGRESS
                            // 其他状态（收缩、膨胀）不显示弧线
                            else -> ArcAnimationType.NONE
                        },
                        showInternalBubbles = when (buttonState) {
                            CheckupButtonState.MANUAL_CHECKING,
                            CheckupButtonState.OPTIMIZING -> true
                            else -> false
                        },
                        particleAnimationState = particleAnimationState,
                        isProcessing = particleAnimationState != ParticleAnimationState.NORMAL,
                        motionSensingEnabled = globalSettings.waterBallMotionSensingEnabled,
                        onParticleAnimationStateChange = { newState ->
                            viewModel.updateParticleAnimationState(newState)
                        },
                        modifier = Modifier
                            .padding(waterBallPadding)
                            .padding(bottom = sectionSpacing)
                    )
                }
            }
        }

        // 状态信息 - 简洁显示
        item {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp)
                    .padding(bottom = sectionSpacing),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = healthStatusDescription,
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = themeContext.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = secondaryStatusText,
                    style = MaterialTheme.typography.bodyLarge,
                    color = themeContext.colorScheme.onSurface.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center
                )
            }
        }

        // 操作按钮区域 - 体检时和优化时隐藏
        if (buttonState != CheckupButtonState.MANUAL_CHECKING &&
            buttonState != CheckupButtonState.OPTIMIZING) {
            item {
                OceanBlueOptimizeButton(
                    isOptimizing = isOptimizing,
                    canOptimize = viewModel.canClick(),
                    buttonText = viewModel.getButtonText(),
                    onOptimize = { viewModel.handleButtonClick() },
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }
        }


    }
    }
}

/**
 * 海洋蓝风格的优化按钮
 */
@Composable
private fun OceanBlueOptimizeButton(
    isOptimizing: Boolean,
    canOptimize: Boolean,
    buttonText: String,
    onOptimize: () -> Unit,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current
    
    Button(
        onClick = onOptimize,
        enabled = canOptimize && !isOptimizing,
        colors = ButtonDefaults.buttonColors(
            containerColor = themeContext.colorScheme.primary, // 使用海洋蓝主题品牌色
            contentColor = Color.White,
            disabledContainerColor = themeContext.colorScheme.primary.copy(alpha = 0.5f),
            disabledContentColor = Color.White.copy(alpha = 0.7f)
        ),
        modifier = modifier
            .fillMaxWidth(0.7f)
            .height(48.dp),
        shape = RoundedCornerShape(24.dp)
    ) {
        if (isOptimizing) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = Color.White,
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = buttonText,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        } else {
            Text(
                text = buttonText,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 海洋蓝风格的权限设置卡片
 */
@Composable
private fun OceanBluePermissionCard(
    missingPermissions: List<String>,
    onRefreshPermissions: () -> Unit,
    modifier: Modifier = Modifier
) {
    val themeContext = LocalThemeContext.current

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "需要开启权限",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = themeContext.colorScheme.error
        )

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = "缺少以下权限：${missingPermissions.joinToString("、")}",
            style = MaterialTheme.typography.bodyMedium,
            color = themeContext.colorScheme.onSurface.copy(alpha = 0.8f),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Button(
            onClick = onRefreshPermissions,
            colors = ButtonDefaults.buttonColors(
                containerColor = themeContext.colorScheme.error,
                contentColor = themeContext.colorScheme.onError
            ),
            shape = RoundedCornerShape(20.dp)
        ) {
            Text("重新检查权限")
        }
    }
}




