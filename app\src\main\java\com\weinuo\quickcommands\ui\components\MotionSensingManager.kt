package com.weinuo.quickcommands.ui.components

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.util.Log
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.State
import kotlin.math.abs

/**
 * 水球体感跟随管理器
 * 
 * 负责监听设备的重力传感器，计算设备倾斜角度，
 * 为水球组件提供体感跟随数据
 */
class MotionSensingManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "MotionSensingManager"
        private const val SENSOR_SAMPLING_RATE = SensorManager.SENSOR_DELAY_UI
        private const val MAX_TILT_ANGLE = 90f // 允许更大的倾斜角度，实现真实水杯效果
        
        @Volatile
        private var INSTANCE: MotionSensingManager? = null
        
        fun getInstance(context: Context): MotionSensingManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MotionSensingManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private var gravitySensor: Sensor? = null
    private var magneticSensor: Sensor? = null
    private var sensorListener: SensorEventListener? = null
    
    // 倾斜数据状态
    private val _tiltX = mutableStateOf(0f) // X轴倾斜角度（左右倾斜）
    private val _tiltY = mutableStateOf(0f) // Y轴倾斜角度（前后倾斜）
    private val _isActive = mutableStateOf(false)
    
    val tiltX: State<Float> = _tiltX
    val tiltY: State<Float> = _tiltY
    val isActive: State<Boolean> = _isActive
    
    /**
     * 开始监听体感数据
     */
    fun startSensing() {
        if (_isActive.value) {
            Log.d(TAG, "Motion sensing already active")
            return
        }
        
        gravitySensor = sensorManager.getDefaultSensor(Sensor.TYPE_GRAVITY)
        magneticSensor = sensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD)

        if (gravitySensor == null) {
            Log.w(TAG, "Gravity sensor not available, trying accelerometer")
            gravitySensor = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
        }

        if (gravitySensor == null) {
            Log.e(TAG, "Gravity/Accelerometer sensor not available for motion sensing")
            return
        }

        if (magneticSensor == null) {
            Log.w(TAG, "Magnetic sensor not available, trying simplified approach")
            // 如果没有磁场传感器，使用简化的倾斜检测
        }
        
        sensorListener = object : SensorEventListener {
            override fun onSensorChanged(event: SensorEvent) {
                handleSensorData(event)
            }
            
            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
                // 不需要处理精度变化
            }
        }
        
        val gravitySuccess = sensorManager.registerListener(
            sensorListener,
            gravitySensor,
            SENSOR_SAMPLING_RATE
        )

        val magneticSuccess = if (magneticSensor != null) {
            sensorManager.registerListener(
                sensorListener,
                magneticSensor,
                SENSOR_SAMPLING_RATE
            )
        } else {
            true // 如果没有磁场传感器，仍然可以工作
        }

        if (gravitySuccess) {
            _isActive.value = true
            Log.d(TAG, "Motion sensing started successfully (gravity: $gravitySuccess, magnetic: $magneticSuccess)")
        } else {
            Log.e(TAG, "Failed to register gravity sensor listener")
        }
    }
    
    /**
     * 停止监听体感数据
     */
    fun stopSensing() {
        if (!_isActive.value) {
            return
        }
        
        sensorListener?.let { listener ->
            sensorManager.unregisterListener(listener)
        }
        
        sensorListener = null
        gravitySensor = null
        magneticSensor = null
        _isActive.value = false
        
        // 重置倾斜数据
        _tiltX.value = 0f
        _tiltY.value = 0f
        
        Log.d(TAG, "Motion sensing stopped")
    }
    
    // 用于计算方向的数组
    private val rotationMatrix = FloatArray(9)
    private val orientationAngles = FloatArray(3)
    private var gravityValues: FloatArray? = null
    private var magneticValues: FloatArray? = null
    private var logCounter = 0 // 用于控制日志频率

    /**
     * 处理传感器数据
     */
    private fun handleSensorData(event: SensorEvent) {
        when (event.sensor.type) {
            Sensor.TYPE_GRAVITY -> {
                gravityValues = event.values.clone()
                if (logCounter % 30 == 0) { // 每30次记录一次日志
                    Log.d(TAG, "Gravity data: ${gravityValues?.contentToString()}")
                }
            }
            Sensor.TYPE_ACCELEROMETER -> {
                // 如果没有重力传感器，使用加速度计
                if (gravitySensor?.type == Sensor.TYPE_ACCELEROMETER) {
                    gravityValues = event.values.clone()
                    if (logCounter % 30 == 0) {
                        Log.d(TAG, "Accelerometer data: ${gravityValues?.contentToString()}")
                    }
                }
            }
            Sensor.TYPE_MAGNETIC_FIELD -> {
                magneticValues = event.values.clone()
                if (logCounter % 30 == 0) {
                    Log.d(TAG, "Magnetic data: ${magneticValues?.contentToString()}")
                }
            }
        }
        logCounter++

        val gravity = gravityValues
        val magnetic = magneticValues

        if (gravity != null) {
            if (magnetic != null) {
                // 使用重力和磁场传感器计算旋转矩阵（精确方法）
                val success = SensorManager.getRotationMatrix(rotationMatrix, null, gravity, magnetic)
                Log.d(TAG, "Rotation matrix calculation success: $success")
                if (success) {
                    SensorManager.getOrientation(rotationMatrix, orientationAngles)

                    // orientationAngles[0]: 方位角 (azimuth)
                    // orientationAngles[1]: 俯仰角 (pitch) - 前后倾斜
                    // orientationAngles[2]: 横滚角 (roll) - 左右倾斜

                    val pitchRad = orientationAngles[1]
                    val rollRad = orientationAngles[2]

                    val pitchDeg = Math.toDegrees(pitchRad.toDouble()).toFloat()
                    val rollDeg = Math.toDegrees(rollRad.toDouble()).toFloat()

                    Log.d(TAG, "Raw angles - Pitch: $pitchDeg, Roll: $rollDeg")

                    // 限制倾斜角度范围
                    val clampedTiltX = rollDeg.coerceIn(-MAX_TILT_ANGLE, MAX_TILT_ANGLE)
                    val clampedTiltY = pitchDeg.coerceIn(-MAX_TILT_ANGLE, MAX_TILT_ANGLE)

                    // 应用平滑滤波，减少抖动
                    val smoothingFactor = 0.8f
                    _tiltX.value = _tiltX.value * smoothingFactor + clampedTiltX * (1 - smoothingFactor)
                    _tiltY.value = _tiltY.value * smoothingFactor + clampedTiltY * (1 - smoothingFactor)

                    // 调试日志（减少频率）
                    if (logCounter % 30 == 0) {
                        Log.d(TAG, "Tilt updated: X=${_tiltX.value}, Y=${_tiltY.value}")
                    }
                }
            } else {
                // 简化方法：仅使用重力传感器
                val gx = gravity[0]
                val gy = gravity[1]
                val gz = gravity[2]

                // 计算倾斜角度（弧度转角度）
                val tiltXRad = kotlin.math.atan2(gx, gz)
                val tiltYRad = kotlin.math.atan2(gy, gz)

                val tiltXDeg = Math.toDegrees(tiltXRad.toDouble()).toFloat()
                val tiltYDeg = Math.toDegrees(tiltYRad.toDouble()).toFloat()

                Log.d(TAG, "Simplified angles - X: $tiltXDeg, Y: $tiltYDeg")

                // 限制倾斜角度范围
                val clampedTiltX = tiltXDeg.coerceIn(-MAX_TILT_ANGLE, MAX_TILT_ANGLE)
                val clampedTiltY = tiltYDeg.coerceIn(-MAX_TILT_ANGLE, MAX_TILT_ANGLE)

                // 应用平滑滤波，减少抖动（降低平滑因子以提高响应速度，实现真实水杯效果）
                val smoothingFactor = 0.3f
                _tiltX.value = _tiltX.value * smoothingFactor + clampedTiltX * (1 - smoothingFactor)
                _tiltY.value = _tiltY.value * smoothingFactor + clampedTiltY * (1 - smoothingFactor)

                // 调试日志（减少频率）
                if (logCounter % 30 == 0) {
                    Log.d(TAG, "Simplified tilt updated: X=${_tiltX.value}, Y=${_tiltY.value}")
                }
            }
        } else {
            Log.d(TAG, "Missing gravity sensor data")
        }
    }
    
    /**
     * 获取归一化的倾斜值
     * @return Pair<Float, Float> X轴和Y轴的归一化倾斜值（-1.0 到 1.0）
     */
    fun getNormalizedTilt(): Pair<Float, Float> {
        val normalizedX = (_tiltX.value / MAX_TILT_ANGLE).coerceIn(-1f, 1f)
        val normalizedY = (_tiltY.value / MAX_TILT_ANGLE).coerceIn(-1f, 1f)
        return Pair(normalizedX, normalizedY)
    }
    
    /**
     * 获取水面倾斜偏移量
     * @param maxOffset 最大偏移量（像素）
     * @return Pair<Float, Float> X轴和Y轴的偏移量
     */
    fun getWaterSurfaceOffset(maxOffset: Float): Pair<Float, Float> {
        val (normalizedX, normalizedY) = getNormalizedTilt()
        return Pair(
            normalizedX * maxOffset,
            normalizedY * maxOffset
        )
    }
}
