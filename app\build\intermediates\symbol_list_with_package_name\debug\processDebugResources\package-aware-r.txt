com.weinuo.quickcommands
attr action
attr alpha
attr argType
attr data
attr dataPattern
attr destination
attr enterAnim
attr exitAnim
attr font
attr fontProviderAuthority
attr fontProviderCerts
attr fontProviderFallbackQuery
attr fontProviderFetchStrategy
attr fontProviderFetchTimeout
attr fontProviderPackage
attr fontProviderQuery
attr fontProviderSystemFontFamily
attr fontStyle
attr fontVariationSettings
attr fontWeight
attr graph
attr lStar
attr launchSingleTop
attr mimeType
attr navGraph
attr nestedScrollViewStyle
attr nullable
attr popEnterAnim
attr popExitAnim
attr popUpTo
attr popUpToInclusive
attr popUpToSaveState
attr queryPatterns
attr restoreState
attr route
attr shortcutMatchRequired
attr startDestination
attr targetPackage
attr ttcIndex
attr uri
color androidx_core_ripple_material_light
color androidx_core_secondary_text_default_material_light
color black
color call_notification_answer_color
color call_notification_decline_color
color notification_action_color_filter
color notification_icon_bg_color
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color vector_tint_color
color vector_tint_theme_color
color white
dimen compat_button_inset_horizontal_material
dimen compat_button_inset_vertical_material
dimen compat_button_padding_horizontal_material
dimen compat_button_padding_vertical_material
dimen compat_control_corner_material
dimen compat_notification_large_icon_max_height
dimen compat_notification_large_icon_max_width
dimen notification_action_icon_size
dimen notification_action_text_size
dimen notification_big_circle_margin
dimen notification_content_margin_start
dimen notification_large_icon_height
dimen notification_large_icon_width
dimen notification_main_column_padding_top
dimen notification_media_narrow_margin
dimen notification_right_icon_size
dimen notification_right_side_padding_top
dimen notification_small_icon_background_padding
dimen notification_small_icon_size_as_large
dimen notification_subtext_size
dimen notification_top_pad
dimen notification_top_pad_large_text
drawable circular_progress_drawable
drawable circular_reminder_background
drawable floating_button_background
drawable haze_noise
drawable ic_add_skyblue
drawable ic_apps_24
drawable ic_call_answer
drawable ic_call_answer_low
drawable ic_call_answer_video
drawable ic_call_answer_video_low
drawable ic_call_decline
drawable ic_call_decline_low
drawable ic_circle_white
drawable ic_clear
drawable ic_close
drawable ic_close_24
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_lightbulb_24
drawable ic_long_press
drawable ic_map_24
drawable ic_music_note_24
drawable ic_notification
drawable ic_pause
drawable ic_play_arrow
drawable ic_radio_button_checked_sky_blue
drawable ic_radio_button_unchecked_sky_blue
drawable ic_record_voice_over
drawable ic_save
drawable ic_screen_rotation_24
drawable ic_search_sky_blue_bold
drawable ic_search_sky_blue_medium
drawable ic_search_sky_blue_regular
drawable ic_settings
drawable ic_share_24
drawable ic_shopping_cart_24
drawable ic_shortcut_command
drawable ic_sky_blue_arrow_right
drawable ic_sky_blue_back_arrow
drawable ic_sky_blue_check_disabled
drawable ic_sky_blue_check_enabled
drawable ic_sky_blue_more_vert
drawable ic_stop
drawable ic_swipe
drawable ic_touch_app
drawable menu_background
drawable menu_item_background
drawable notification_action_background
drawable notification_bg
drawable notification_bg_low
drawable notification_bg_low_normal
drawable notification_bg_low_pressed
drawable notification_bg_normal
drawable notification_bg_normal_pressed
drawable notification_icon_background
drawable notification_oversize_large_icon_bg
drawable notification_template_icon_bg
drawable notification_template_icon_low_bg
drawable notification_tile_bg
drawable notify_panel_notification_icon_bg
drawable overlay_background
drawable overlay_background_md3
drawable template_anti_embarrassment_mode
drawable template_screen_off_network
drawable template_screen_on_network
drawable widget_background
id accessibility_action_clickable_span
id accessibility_custom_action_0
id accessibility_custom_action_1
id accessibility_custom_action_10
id accessibility_custom_action_11
id accessibility_custom_action_12
id accessibility_custom_action_13
id accessibility_custom_action_14
id accessibility_custom_action_15
id accessibility_custom_action_16
id accessibility_custom_action_17
id accessibility_custom_action_18
id accessibility_custom_action_19
id accessibility_custom_action_2
id accessibility_custom_action_20
id accessibility_custom_action_21
id accessibility_custom_action_22
id accessibility_custom_action_23
id accessibility_custom_action_24
id accessibility_custom_action_25
id accessibility_custom_action_26
id accessibility_custom_action_27
id accessibility_custom_action_28
id accessibility_custom_action_29
id accessibility_custom_action_3
id accessibility_custom_action_30
id accessibility_custom_action_31
id accessibility_custom_action_4
id accessibility_custom_action_5
id accessibility_custom_action_6
id accessibility_custom_action_7
id accessibility_custom_action_8
id accessibility_custom_action_9
id action_clear
id action_container
id action_divider
id action_image
id action_save
id action_settings
id action_text
id actions
id androidx_compose_ui_view_composition_context
id async
id blocking
id chronometer
id circular_reminder_button
id compose_view_saveable_id_tag
id consume_window_insets_tag
id dialog_button
id edit_text_id
id forever
id hide_graphics_layer_in_inspector_tag
id hide_ime_id
id hide_in_inspector_tag
id icon
id icon_group
id info
id inspection_slot_table_set
id is_pooling_container_tag
id italic
id iv_reminder_icon
id line1
id line3
id nav_controller_view_tag
id normal
id notification_background
id notification_main_column
id notification_main_column_container
id pooling_container_listener_holder_tag
id progress_auto_dismiss
id report_drawn
id right_icon
id right_side
id tag_accessibility_actions
id tag_accessibility_clickable_spans
id tag_accessibility_heading
id tag_accessibility_pane_title
id tag_compat_insets_dispatch
id tag_on_apply_window_listener
id tag_on_receive_content_listener
id tag_on_receive_content_mime_types
id tag_screen_reader_focusable
id tag_state_description
id tag_system_bar_state_monitor
id tag_transition_group
id tag_unhandled_key_event_manager
id tag_unhandled_key_listeners
id tag_window_insets_animation_callback
id text
id text2
id time
id title
id view_tree_disjoint_parent
id view_tree_lifecycle_owner
id view_tree_on_back_pressed_dispatcher_owner
id view_tree_saved_state_registry_owner
id view_tree_view_model_store_owner
id widget_text_line1
id widget_text_line2
id wrapped_composition_tag
integer m3c_window_layout_in_display_cutout_mode
integer status_bar_notification_info_maxnum
layout custom_dialog
layout ime_base_split_test_activity
layout ime_secondary_split_test_activity
layout notification_action
layout notification_action_tombstone
layout notification_template_custom_big
layout notification_template_icon_group
layout notification_template_part_chronometer
layout notification_template_part_time
layout overlay_smart_reminder
layout widget_one_click_command
menu advanced_recording_menu
mipmap ic_launcher
mipmap ic_launcher_round
string accessibility_service_description
string accessibility_service_disable
string accessibility_service_enable
string accessibility_service_go_to_settings
string accessibility_service_manual_title
string accessibility_service_name
string accessibility_service_system_description
string accessibility_service_title
string account_selection
string add_cleanup_rule
string add_cleanup_rule_description
string address_reminder_description
string address_reminder_message
string address_reminder_status_disabled
string address_reminder_status_enabled
string address_reminder_title
string advanced_cleanup_strategy
string advanced_cleanup_strategy_description
string advanced_memory_config
string airplane_mode_changed
string airplane_mode_changed_description
string ambient_display
string ambient_display_description
string androidx_startup
string app_detection_any_app
string app_detection_selected_apps
string app_execute_javascript
string app_execute_javascript_description
string app_execute_shell_script
string app_execute_shell_script_description
string app_force_stop_app
string app_force_stop_app_description
string app_freeze_app
string app_freeze_app_description
string app_importance_management
string app_importance_management_description
string app_interface_click
string app_interface_click_description
string app_launch_app
string app_launch_app_description
string app_lifecycle
string app_lifecycle_description
string app_link_reminder_description
string app_link_reminder_status_disabled
string app_link_reminder_status_enabled
string app_link_reminder_status_unconfigured
string app_link_reminder_title
string app_management
string app_management_description
string app_name
string app_open_website
string app_open_website_description
string app_package_management
string app_package_management_description
string app_screen_content
string app_screen_content_description
string app_selection
string app_state
string app_state_change
string app_state_change_description
string app_state_description
string app_tasker_locale_plugin
string app_tasker_locale_plugin_description
string app_trigger_all_apps
string app_trigger_any_app
string app_unfreeze_app
string app_unfreeze_app_description
string application_task
string application_task_description
string auto_clicker_accessibility_service_description
string auto_clicker_accessibility_service_name
string auto_rotate_disable
string auto_rotate_enable
string auto_sync_state
string auto_sync_state_description
string autofill
string back
string batch_delete_confirm
string batch_delete_quick_commands
string batch_delete_warning
string battery_battery_level
string battery_battery_level_description
string battery_charging_state
string battery_charging_state_description
string battery_state
string battery_state_description
string call_notification_answer_action
string call_notification_answer_video_action
string call_notification_decline_action
string call_notification_hang_up_action
string call_notification_incoming_text
string call_notification_ongoing_text
string call_notification_screening_text
string camera_flashlight_control
string camera_flashlight_control_description
string camera_open_last_photo
string camera_open_last_photo_description
string camera_record_video
string camera_record_video_description
string camera_screenshot
string camera_screenshot_description
string camera_take_photo
string camera_take_photo_description
string camera_task
string camera_task_description
string cancel
string change_map_apps
string change_music_apps
string check_text_content
string checkup_button
string clipboard_changed
string clipboard_changed_description
string close_drawer
string close_sheet
string comm_call_active
string comm_call_active_description
string comm_call_ended
string comm_call_ended_description
string comm_incoming_call
string comm_incoming_call_description
string comm_outgoing_call
string comm_outgoing_call_description
string comm_sms_received
string comm_sms_received_description
string comm_sms_sent
string comm_sms_sent_description
string communication_state
string communication_state_description
string configure_item
string confirm_configuration
string conn_bluetooth_state
string conn_bluetooth_state_description
string conn_mobile_data
string conn_mobile_data_description
string conn_wifi_network
string conn_wifi_network_description
string conn_wifi_state
string conn_wifi_state_description
string connection_state
string connection_state_description
string connectivity_airplane_mode_control
string connectivity_airplane_mode_control_description
string connectivity_bluetooth_control
string connectivity_bluetooth_control_description
string connectivity_hotspot_control
string connectivity_hotspot_control_description
string connectivity_mobile_data_control
string connectivity_mobile_data_control_description
string connectivity_network_check
string connectivity_network_check_description
string connectivity_nfc_control
string connectivity_nfc_control_description
string connectivity_send_intent
string connectivity_send_intent_description
string connectivity_task
string connectivity_task_description
string connectivity_wifi_control
string connectivity_wifi_control_description
string contact_selection
string custom_app_platform_config
string custom_shopping_platform_config
string dark_theme_changed
string dark_theme_changed_description
string datetime_alarm
string datetime_alarm_description
string datetime_stopwatch
string datetime_stopwatch_description
string datetime_task
string datetime_task_description
string datetime_voice_time_announcement
string datetime_voice_time_announcement_description
string default_error_message
string default_keyboard
string default_keyboard_description
string default_popup_window_title
string delete
string delete_operation_irreversible
string delete_quick_command
string delete_quick_command_confirm
string delete_quick_command_warning
string demo_mode
string demo_mode_description
string detailed_configuration
string device_action_share_text
string device_action_share_text_description
string device_action_task
string device_action_task_description
string device_boot_completed
string device_boot_completed_description
string device_clipboard_changed
string device_clipboard_changed_description
string device_event
string device_event_description
string device_gps_state
string device_gps_state_description
string device_logcat_message
string device_logcat_message_description
string device_settings
string device_settings_accessibility_service
string device_settings_accessibility_service_description
string device_settings_auto_rotate
string device_settings_auto_rotate_description
string device_settings_description
string device_settings_display_density
string device_settings_display_density_description
string device_settings_driving_mode
string device_settings_driving_mode_description
string device_settings_enter_screensaver
string device_settings_enter_screensaver_description
string device_settings_font_size
string device_settings_font_size_description
string device_settings_immersive_mode
string device_settings_immersive_mode_description
string device_settings_invert_colors
string device_settings_invert_colors_description
string device_settings_keyboard_hint
string device_settings_keyboard_hint_description
string dialog_content
string dialog_content_placeholder
string dialog_settings
string dialog_title
string dialog_title_placeholder
string digital_assistant
string digital_assistant_description
string disable
string dock_state
string dock_state_description
string driving_mode
string driving_mode_description
string dropdown_menu
string edit_abort_condition
string edit_abort_condition_description
string edit_condition
string edit_condition_description
string edit_task
string edit_task_description
string enable
string executing_quick_command
string execution_mode
string experimental_features
string experimental_features_description
string experimental_features_enabled
string file_file_operation
string file_file_operation_description
string file_open_file
string file_open_file_description
string file_operation_task
string file_operation_task_description
string file_write_file
string file_write_file_description
string flashlight_reminder_description
string flashlight_reminder_status_disabled
string flashlight_reminder_status_enabled
string flashlight_reminder_status_unconfigured
string flashlight_reminder_title
string font_size
string font_size_description
string font_size_percentage
string font_size_settings
string font_weight_bold
string font_weight_medium
string font_weight_regular
string font_weight_selection_title
string gesture_recognition_accessibility_service_description
string gesture_recognition_accessibility_service_name
string gesture_recording_edit
string gesture_recording_edit_description
string go_to_settings
string gps_state
string gps_state_description
string icon_weight_bold
string icon_weight_medium
string icon_weight_regular
string icon_weight_selection_title
string in_progress
string indeterminate
string info_message_ringtone
string info_message_ringtone_description
string info_send_email
string info_send_email_description
string info_send_sms
string info_send_sms_description
string info_show_dialog
string info_show_dialog_description
string info_show_toast
string info_show_toast_description
string information_task
string information_task_description
string intelligent_link_recognition_description
string intelligent_link_recognition_enabled
string intelligent_link_recognition_help
string intelligent_link_recognition_title
string intent_received
string intent_received_description
string interface_click
string interface_click_description
string interface_interaction_accessibility_service_description
string interface_interaction_accessibility_service_name
string interface_interaction_service_not_enabled
string invalid_command
string invert_colors
string invert_colors_description
string invert_colors_off
string invert_colors_on
string invert_colors_operation
string keyboard_hint
string keyboard_hint_description
string language_chinese
string language_english
string language_selection_title
string language_settings
string language_settings_description
string language_system_default
string lifecycle
string lifecycle_description
string location_force_location_update
string location_force_location_update_description
string location_get_location
string location_get_location_description
string location_set_location_update_frequency
string location_set_location_update_frequency_description
string location_share_location
string location_share_location_description
string location_task
string location_task_description
string location_toggle_location_service
string location_toggle_location_service_description
string logcat_message
string logcat_message_description
string login_attempt_failed
string login_attempt_failed_description
string m3c_bottom_sheet_collapse_description
string m3c_bottom_sheet_dismiss_description
string m3c_bottom_sheet_drag_handle_description
string m3c_bottom_sheet_expand_description
string m3c_bottom_sheet_pane_title
string m3c_date_input_headline
string m3c_date_input_headline_description
string m3c_date_input_invalid_for_pattern
string m3c_date_input_invalid_not_allowed
string m3c_date_input_invalid_year_range
string m3c_date_input_label
string m3c_date_input_no_input_description
string m3c_date_input_title
string m3c_date_picker_headline
string m3c_date_picker_headline_description
string m3c_date_picker_navigate_to_year_description
string m3c_date_picker_no_selection_description
string m3c_date_picker_scroll_to_earlier_years
string m3c_date_picker_scroll_to_later_years
string m3c_date_picker_switch_to_calendar_mode
string m3c_date_picker_switch_to_day_selection
string m3c_date_picker_switch_to_input_mode
string m3c_date_picker_switch_to_next_month
string m3c_date_picker_switch_to_previous_month
string m3c_date_picker_switch_to_year_selection
string m3c_date_picker_title
string m3c_date_picker_today_description
string m3c_date_picker_year_picker_pane_title
string m3c_date_range_input_invalid_range_input
string m3c_date_range_input_title
string m3c_date_range_picker_day_in_range
string m3c_date_range_picker_end_headline
string m3c_date_range_picker_scroll_to_next_month
string m3c_date_range_picker_scroll_to_previous_month
string m3c_date_range_picker_start_headline
string m3c_date_range_picker_title
string m3c_dialog
string m3c_dropdown_menu_collapsed
string m3c_dropdown_menu_expanded
string m3c_dropdown_menu_toggle
string m3c_search_bar_search
string m3c_snackbar_dismiss
string m3c_suggestions_available
string m3c_time_picker_am
string m3c_time_picker_hour
string m3c_time_picker_hour_24h_suffix
string m3c_time_picker_hour_selection
string m3c_time_picker_hour_suffix
string m3c_time_picker_hour_text_field
string m3c_time_picker_minute
string m3c_time_picker_minute_selection
string m3c_time_picker_minute_suffix
string m3c_time_picker_minute_text_field
string m3c_time_picker_period_toggle_description
string m3c_time_picker_pm
string m3c_tooltip_long_press_label
string m3c_tooltip_pane_description
string manual_dynamic_shortcut
string manual_dynamic_shortcut_description
string manual_fingerprint_gesture
string manual_fingerprint_gesture_description
string manual_home_button_long_press
string manual_home_button_long_press_description
string manual_media_key_press
string manual_media_key_press_description
string manual_static_shortcut
string manual_static_shortcut_description
string manual_trigger
string manual_trigger_description
string manual_volume_key_press
string manual_volume_key_press_description
string manual_widget_update
string manual_widget_update_description
string match_options
string media_microphone_recording
string media_microphone_recording_description
string media_multimedia_control
string media_multimedia_control_description
string media_play_stop_sound
string media_play_stop_sound_description
string media_task
string media_task_description
string memory_learning_data
string music_app_reminder_description
string music_app_reminder_status_configured
string music_app_reminder_status_disabled
string music_app_reminder_status_enabled
string music_app_reminder_status_unconfigured
string music_app_reminder_title
string music_playback_state
string music_playback_state_description
string nav_command_templates
string nav_global_settings
string nav_phone_checkup
string nav_quick_commands
string nav_smart_reminders
string navigation_menu
string new_app_reminder_description
string new_app_reminder_status_disabled
string new_app_reminder_status_enabled
string new_app_reminder_status_unconfigured
string new_app_reminder_title
string no_quick_commands
string no_search_results
string no_template_search_results
string no_templates
string not_selected
string notification_cancel_notification
string notification_cancel_notification_description
string notification_event
string notification_event_description
string notification_listener_service_description
string notification_listener_service_name
string notification_show_notification
string notification_show_notification_description
string notification_task
string notification_task_description
string optimization_complete
string optimization_failed
string optimize_button
string optimizing
string package_management
string package_management_description
string package_name
string phone_answer_call
string phone_answer_call_description
string phone_checkup_title
string phone_clear_call_log
string phone_clear_call_log_description
string phone_make_call
string phone_make_call_description
string phone_open_call_log
string phone_open_call_log_description
string phone_reject_call
string phone_reject_call_description
string phone_ringtone_settings
string phone_ringtone_settings_description
string phone_status_excellent
string phone_status_good
string phone_status_poor
string phone_task
string phone_task_description
string power_save_mode
string power_save_mode_description
string quick_command_aborted
string quick_command_completed
string quick_command_edit
string quick_command_form
string quick_command_new
string quick_command_not_found
string quick_command_not_found_simple
string quick_commands_title
string range_end
string range_start
string ringer_mode_changed
string ringer_mode_changed_description
string ringtone_selection
string running_apps_count
string save
string screen_brightness_control
string screen_brightness_control_description
string screen_content
string screen_content_description
string screen_content_text
string screen_content_text_label
string screen_content_text_placeholder
string screen_control_task
string screen_control_task_description
string screen_event_auto_rotate_disabled
string screen_event_auto_rotate_enabled
string screen_event_off
string screen_event_on
string screen_event_unlocked
string screen_keep_device_awake
string screen_keep_device_awake_description
string screen_rotation_reminder_description
string screen_rotation_reminder_status_disabled
string screen_rotation_reminder_status_enabled
string screen_rotation_reminder_status_unconfigured
string screen_rotation_reminder_title
string screen_state
string screen_state_description
string screen_text_check_failed
string search_apps
string search_field_icon_weight
string search_field_icon_weight_description
string search_field_placeholder_font_weight
string search_field_placeholder_font_weight_description
string search_field_settings
string search_quick_commands
string search_smart_reminders
string search_templates
string select_map_apps
string select_music_apps
string selected
string sensor_activity_recognition
string sensor_activity_recognition_description
string sensor_flip_sensor
string sensor_flip_sensor_description
string sensor_light_sensor
string sensor_light_sensor_description
string sensor_orientation_sensor
string sensor_orientation_sensor_description
string sensor_proximity_sensor
string sensor_proximity_sensor_description
string sensor_shake_sensor
string sensor_shake_sensor_description
string sensor_sleep_sensor
string sensor_sleep_sensor_description
string sensor_state
string sensor_state_description
string share_target_selection
string share_text
string share_text_description
string share_url_reminder_description
string share_url_reminder_status_disabled
string share_url_reminder_status_enabled
string share_url_reminder_status_unconfigured
string share_url_reminder_title
string shell_script
string shell_script_placeholder
string shell_script_placeholder_command
string shell_script_settings
string shizuku_category_app_management
string shizuku_category_key_simulation
string shizuku_category_network
string shizuku_category_screen_operation
string shizuku_category_system
string shizuku_install_guide
string shizuku_not_installed
string shizuku_not_running
string shizuku_permission_required
string shopping_app_reminder_description
string shopping_app_reminder_status_disabled
string shopping_app_reminder_status_enabled
string shopping_app_reminder_status_unconfigured
string shopping_app_reminder_title
string shortcut_1_long_label
string shortcut_1_short_label
string shortcut_2_long_label
string shortcut_2_short_label
string shortcut_3_long_label
string shortcut_3_short_label
string shortcut_4_long_label
string shortcut_4_short_label
string shortcut_not_configured
string sim_card_state
string sim_card_state_description
string smart_reminder_change_app
string smart_reminder_config_error
string smart_reminder_config_not_found
string smart_reminder_configure
string smart_reminder_configured
string smart_reminder_detail_config
string smart_reminder_detail_settings
string smart_reminder_needs_configuration
string smart_reminder_ready_to_use
string smart_reminder_select_app
string smart_reminder_selected_apps_count
string smart_reminder_status_label
string smart_reminder_tap_to_setup
string smart_reminder_type_not_found
string smart_reminders_description
string smart_reminders_title
string snackbar_pane_title
string state_change
string state_change_description
string state_empty
string state_off
string state_on
string status_bar_notification_info_overflow
string stopwatch_selection
string storage_permission_description
string storage_permission_required
string sun_event_sunrise
string sun_event_sunset
string switch_operation_off
string switch_operation_on
string switch_operation_toggle
string switch_role
string system_operation_accessibility_service_description
string system_operation_accessibility_service_name
string system_setting_changed
string system_setting_changed_description
string system_settings
string system_settings_description
string tab
string tap_to_select_map_apps
string tap_to_select_music_apps
string task_alarm_reminder
string task_alarm_reminder_description
string task_app_management
string task_app_management_description
string task_device_settings
string task_device_settings_description
string task_file_operation
string task_file_operation_description
string task_location
string task_location_description
string task_log
string task_log_description
string task_log_task
string task_log_task_description
string task_media_task
string task_media_task_description
string task_network
string task_network_description
string task_phone
string task_phone_description
string task_phone_task
string task_phone_task_description
string task_screen_control
string task_screen_control_description
string task_screen_control_task
string task_screen_control_task_description
string tasker_locale_plugin
string tasker_locale_plugin_description
string tasks_count
string template_anti_embarrassment_mode_desc
string template_anti_embarrassment_mode_title
string template_auto_brightness_desc
string template_auto_brightness_title
string template_battery_saver_desc
string template_battery_saver_title
string template_category_automation
string template_category_display
string template_category_network
string template_category_power
string template_category_system
string template_do_not_disturb_desc
string template_do_not_disturb_title
string template_percent
string template_screen_off_network_desc
string template_screen_off_network_title
string template_screen_on_network_desc
string template_screen_on_network_title
string text_content_label
string text_content_placeholder
string time_based
string time_based_description
string time_condition_delayed_trigger
string time_condition_delayed_trigger_description
string time_condition_periodic_time
string time_condition_periodic_time_description
string time_condition_periodic_trigger
string time_condition_scheduled_time
string time_condition_scheduled_time_description
string time_condition_stopwatch
string time_condition_stopwatch_description
string time_condition_sun_event
string time_condition_sun_event_description
string time_condition_time_period
string time_condition_time_period_description
string toggle
string tooltip_description
string tooltip_label
string trigger_mode
string unified_configuration
string update_now
string usage_stats_permission_description
string usage_stats_permission_required
string volume_changed
string volume_changed_description
string volume_do_not_disturb
string volume_do_not_disturb_description
string volume_speakerphone_control
string volume_speakerphone_control_description
string volume_task
string volume_task_description
string volume_vibration_mode
string volume_vibration_mode_description
string volume_volume_adjust
string volume_volume_adjust_description
string volume_volume_change
string volume_volume_change_description
string volume_volume_popup
string volume_volume_popup_description
string widget_1_label
string widget_2_label
string widget_3_label
string widget_4_label
string widget_update_completed
string widget_update_enabled
string widget_update_enabled_description
string widget_update_interval
string widget_update_interval_error
string widget_update_interval_hint
string widget_update_settings
style DialogWindowTheme
style EdgeToEdgeFloatingDialogTheme
style EdgeToEdgeFloatingDialogWindowTheme
style FloatingDialogTheme
style FloatingDialogWindowTheme
style TextAppearance_Compat_Notification
style TextAppearance_Compat_Notification_Info
style TextAppearance_Compat_Notification_Line2
style TextAppearance_Compat_Notification_Time
style TextAppearance_Compat_Notification_Title
style Theme_QuickCommands
style Theme_QuickCommands_NoActionBar
style Widget_Compat_NotificationActionContainer
style Widget_Compat_NotificationActionText
styleable ActivityNavigator android_name action data dataPattern targetPackage
styleable Capability queryPatterns shortcutMatchRequired
styleable ColorStateListItem android_color android_alpha android_lStar alpha lStar
styleable FontFamily fontProviderAuthority fontProviderCerts fontProviderFallbackQuery fontProviderFetchStrategy fontProviderFetchTimeout fontProviderPackage fontProviderQuery fontProviderSystemFontFamily
styleable FontFamilyFont android_font android_fontWeight android_fontStyle android_ttcIndex android_fontVariationSettings font fontStyle fontVariationSettings fontWeight ttcIndex
styleable GradientColor android_startColor android_endColor android_type android_centerX android_centerY android_gradientRadius android_tileMode android_centerColor android_startX android_startY android_endX android_endY
styleable GradientColorItem android_color android_offset
styleable NavAction android_id destination enterAnim exitAnim launchSingleTop popEnterAnim popExitAnim popUpTo popUpToInclusive popUpToSaveState restoreState
styleable NavArgument android_name android_defaultValue argType nullable
styleable NavDeepLink android_autoVerify action mimeType uri
styleable NavGraphNavigator startDestination
styleable NavHost navGraph
styleable NavInclude graph
styleable Navigator android_label android_id route
xml accessibility_service_config
xml auto_clicker_accessibility_service_config
xml backup_rules
xml data_extraction_rules
xml device_admin
xml file_provider_paths
xml gesture_recognition_accessibility_service_config
xml interface_interaction_accessibility_service_config
xml one_click_command_widget_1_info
xml one_click_command_widget_2_info
xml one_click_command_widget_3_info
xml one_click_command_widget_4_info
xml shortcuts
xml system_operation_accessibility_service_config
