package com.weinuo.quickcommands.ui.screens.skyblue

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.material3.TopAppBarScrollBehavior
import com.weinuo.quickcommands.ui.components.integrated.IntegratedTopAppBarScrollBehavior
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Switch
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Check
import com.weinuo.quickcommands.ui.components.themed.ThemedCard
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueSwitch
import com.weinuo.quickcommands.ui.theme.config.DividerConfig
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.manager.CardStyleConfigurationManager
import com.weinuo.quickcommands.ui.theme.manager.PageLayoutConfigurationManager
import com.weinuo.quickcommands.ui.theme.system.AppTheme
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.components.integrated.SetIntegratedTopAppBar
import com.weinuo.quickcommands.ui.components.integrated.rememberIntegratedTopAppBarConfig
import com.weinuo.quickcommands.ui.components.integrated.rememberIntegratedTopAppBarScrollBehavior
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.TextButton
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.graphics.vector.ImageVector
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.components.integrated.IntegratedTopAppBar
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.ui.theme.config.BlurComponent

import com.weinuo.quickcommands.ui.components.themed.ThemedScaffold
import com.weinuo.quickcommands.utils.ExperimentalFeatureDetector
import com.weinuo.quickcommands.ui.effects.HazeManager
// import dev.chrisbanes.haze.hazeSource // 不再需要，由ThemedScaffold内部处理
import com.weinuo.quickcommands.utils.LanguageManager
import com.weinuo.quickcommands.utils.LocaleHelper
import com.weinuo.quickcommands.utils.withAppLanguage
import com.weinuo.quickcommands.widget.WidgetUpdateManager
import com.weinuo.quickcommands.ui.theme.manager.SkyBlueColorConfigurationManager
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.ui.activities.AppSelectionActivity
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueDialog
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueDialogButton
import com.weinuo.quickcommands.ui.theme.manager.DialogSpacingConfigurationManager
import com.weinuo.quickcommands.ui.components.skyblue.IOSStyleNumberInput
import com.weinuo.quickcommands.model.GlobalSettings
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState

/**
 * 卡片样式对话框类型枚举
 */
enum class CardStyleDialogType(
    val title: String,
    val description: String,
    val range: IntRange,
    val unit: String
) {
    CORNER_RADIUS("圆角大小", "调整卡片的圆角大小，范围：0-50dp", 0..50, "dp"),
    HORIZONTAL_PADDING("水平内边距", "调整卡片的水平内边距，范围：0-50dp", 0..50, "dp"),
    VERTICAL_PADDING("内容卡片垂直边距", "调整内容卡片（快捷指令、模板等）的垂直内边距，范围：0-50dp", 0..50, "dp"),
    SETTINGS_VERTICAL_PADDING("设置卡片垂直边距", "调整设置界面卡片的垂直内边距，范围：0-50dp", 0..50, "dp"),
    ITEM_SPACING("卡片间距", "调整页面中卡片之间的间距，范围：0-30dp", 0..30, "dp"),
    SECTION_SPACING("区域间距", "调整卡片之间的间距，范围：0-50dp", 0..50, "dp"),
    CONTENT_VERTICAL_SPACING("卡片内容垂直间距", "调整卡片内部元素之间的垂直间距，范围：0-40dp", 0..40, "dp"),
    CONTENT_HORIZONTAL_SPACING("卡片内容水平间距", "调整卡片内容的水平间距，范围：0-30dp", 0..30, "dp"),
    SELECTED_BORDER_WIDTH("选中边框宽度", "调整卡片选中时的边框宽度，范围：0-10dp", 0..10, "dp"),
    CARD_TITLE_FONT_SIZE("卡片标题字体大小", "调整快捷指令名称、模板标题和提醒名称的字体大小，范围：12-32sp", 12..32, "sp"),
    CARD_CONTENT_FONT_SIZE("卡片描述字体大小", "调整指令描述、模板描述和提醒描述的字体大小，范围：10-28sp", 10..28, "sp"),
    CARD_ICON_SIZE("快捷指令图标大小", "调整快捷指令卡片中图标的大小，范围：24-80dp", 24..80, "dp")
}

/**
 * 主题颜色设置对话框类型枚举
 */
enum class ThemeColorDialogType(
    val title: String,
    val colorProperty: String
) {
    // 主要颜色系统
    PRIMARY("品牌主色", "skyBluePrimary"),
    ON_PRIMARY("主色上的文本", "skyBlueOnPrimary"),
    PRIMARY_CONTAINER("主色容器", "skyBluePrimaryContainer"),
    ON_PRIMARY_CONTAINER("主色容器上的文本", "skyBlueOnPrimaryContainer"),

    // 次要颜色系统
    SECONDARY("次要色", "skyBlueSecondary"),
    ON_SECONDARY("次要色上的文本", "skyBlueOnSecondary"),
    SECONDARY_CONTAINER("次要色容器", "skyBlueSecondaryContainer"),
    ON_SECONDARY_CONTAINER("次要色容器上的文本", "skyBlueOnSecondaryContainer"),

    // 表面颜色系统
    BACKGROUND("背景色", "skyBlueBackground"),
    ON_BACKGROUND("背景上的文本", "skyBlueOnBackground"),
    SURFACE("表面色", "skyBlueSurface"),
    ON_SURFACE("表面上的文本", "skyBlueOnSurface"),

    // 扩展颜色系统
    CONFIRM("确认色", "skyBlueConfirm"),
    FONT_EMPHASIZE("强调文本色", "skyBlueFontEmphasize"),
    ICON_EMPHASIZE("强调图标色", "skyBlueIconEmphasize"),
    ICON_SUB_EMPHASIZE("辅助强调图标色", "skyBlueIconSubEmphasize"),
    BACKGROUND_EMPHASIZE("强调背景色", "skyBlueBackgroundEmphasize"),
    BACKGROUND_FOCUS("焦点背景色", "skyBlueBackgroundFocus"),

    // 底部导航栏颜色
    BOTTOM_NAV_BACKGROUND("导航栏背景色", "skyBlueBottomNavBackground"),
    BOTTOM_NAV_SELECTED_ICON("导航栏选中图标颜色", "skyBlueBottomNavSelectedIcon"),
    BOTTOM_NAV_UNSELECTED_ICON("导航栏未选中图标颜色", "skyBlueBottomNavUnselectedIcon"),

    // 标题栏颜色
    TOP_BAR_BACKGROUND("标题栏背景色", "skyBlueTopBarBackground")
}

/**
 * 底部导航栏样式对话框类型枚举
 */
enum class BottomNavStyleDialogType(
    val title: String,
    val range: IntRange,
    val unit: String
) {
    HEIGHT("导航栏高度", 40..200, "dp"),
    HORIZONTAL_PADDING("水平内边距", 0..60, "dp"),
    VERTICAL_PADDING("垂直内边距", 0..40, "dp"),
    ITEM_CORNER_RADIUS("导航项圆角", 0..50, "dp"),
    ITEM_OUTER_PADDING("导航项外边距", 0..20, "dp"),
    ITEM_INNER_PADDING("导航项内边距", 0..30, "dp"),
    ICON_SIZE("图标大小", 16..48, "dp"),
    ICON_TEXT_SPACING("图标文字间距", 0..20, "dp"),
    TEXT_FONT_SIZE("文字大小", 8..24, "sp")
}

/**
 * 标题栏设置对话框类型枚举
 */
enum class TopAppBarDialogType(
    val title: String,
    val range: IntRange,
    val unit: String
) {
    HEIGHT("标题栏高度", 24..200, "dp"),
    TITLE_FONT_SIZE("标题字体大小", 8..60, "sp"),
    TITLE_VERTICAL_OFFSET("标题垂直偏移", -100..100, "dp"),
    TITLE_HORIZONTAL_OFFSET("标题水平偏移", -200..200, "dp"),
    SCREEN_TITLE_FONT_SIZE("界面标题字体大小", 12..40, "sp"),
    FORM_SECTION_TITLE_FONT_SIZE("表单标题字体大小", 10..24, "sp")
}

/**
 * 标题栏按钮配置对话框类型枚举
 */
enum class TopAppBarButtonDialogType(
    val title: String,
    val range: IntRange,
    val unit: String
) {
    CIRCLE_BACKGROUND_SIZE("按钮背景尺寸", 24..80, "dp"),
    CIRCLE_BACKGROUND_HORIZONTAL_MARGIN("按钮边缘间距", 0..50, "dp"),
    CIRCLE_BACKGROUND_RIGHT_MARGIN("按钮标题间距", 0..30, "dp")
}

/**
 * 页面布局对话框类型枚举
 */
enum class PageLayoutDialogType(
    val title: String,
    val description: String,
    val range: IntRange,
    val unit: String
) {
    CONTENT_HORIZONTAL_PADDING("页面水平边距", "调整页面内容的水平边距，范围：8-32dp", 8..32, "dp"),
    SEARCH_FIELD_MARGIN("搜索框边距", "调整搜索框的外边距，范围：8-32dp", 8..32, "dp"),
    BOTTOM_PADDING("页面底部边距", "调整页面底部边距（为底部导航栏留空间），范围：60-120dp", 60..120, "dp"),
    HEADER_SPACING("页面标题间距", "调整页面标题的间距，范围：8-32dp", 8..32, "dp"),
    SCROLL_CONTENT_SPACING("滚动内容间距", "调整滚动内容的间距，范围：4-16dp", 4..16, "dp")
}

/**
 * UI间距对话框类型枚举
 */
enum class UISpacingDialogType(
    val title: String,
    val description: String,
    val range: IntRange,
    val unit: String
) {
    SETTINGS_ITEM_VERTICAL_PADDING("设置项垂直间距", "调整设置项的上下间距，范围：0-24dp", 0..24, "dp"),
    DIVIDER_HORIZONTAL_PADDING("分割线水平间距", "调整分割线左右缩进距离，范围：0-32dp", 0..32, "dp"),
    SETTINGS_CARD_PADDING("设置卡片内边距", "调整设置卡片的内边距，范围：0-32dp", 0..32, "dp"),
    SETTINGS_ITEM_SPACING("设置项间距", "调整设置项之间的间距，范围：0-32dp", 0..32, "dp"),
    SETTINGS_TITLE_SPACING("设置标题间距", "调整设置标题与内容的间距，范围：0-16dp", 0..16, "dp"),
    SETTINGS_DESCRIPTION_SPACING("设置描述间距", "调整设置描述与标题的间距，范围：0-12dp", 0..12, "dp"),
    SETTINGS_GROUP_TITLE_HORIZONTAL_PADDING("分组标题水平间距", "调整设置分组标题（如\"模糊设置\"）的左右间距，范围：0-32dp", 0..32, "dp"),
    SETTINGS_GROUP_TITLE_TOP_PADDING("分组标题上边距", "调整设置分组标题的上边距，范围：0-60dp", 0..60, "dp"),
    SETTINGS_GROUP_TITLE_BOTTOM_PADDING("分组标题下边距", "调整设置分组标题的下边距，范围：0-30dp", 0..30, "dp"),
    GLOBAL_SETTINGS_ITEM_SPACING("全局设置界面间距", "调整全局设置界面中item之间的间距，范围：0-24dp", 0..24, "dp")
}

/**
 * 对话框间距对话框类型枚举
 */
enum class DialogSpacingDialogType(
    val title: String,
    val description: String,
    val range: IntRange,
    val unit: String
) {
    OUTER_PADDING("对话框外边距", "调整对话框的外边距，范围：0-30dp", 0..30, "dp"),
    ICON_BOTTOM_PADDING("图标下方间距", "调整图标与标题之间的间距，范围：0-30dp", 0..30, "dp"),
    TITLE_BOTTOM_PADDING("标题下方间距", "调整标题与内容之间的间距，范围：0-30dp", 0..30, "dp"),
    CONTENT_BOTTOM_PADDING("内容下方间距", "调整内容与按钮之间的间距，范围：0-20dp", 0..20, "dp"),
    BUTTON_TOP_PADDING("按钮上方间距", "调整按钮的上方间距，范围：0-20dp", 0..20, "dp"),
    BUTTON_BOTTOM_PADDING("按钮下方间距", "调整按钮的下方间距，范围：0-30dp", 0..30, "dp"),
    TITLE_FONT_SIZE("标题字体大小", "调整对话框标题的字体大小，范围：12-24sp", 12..24, "sp"),
    DIVIDER_HORIZONTAL_PADDING("分割线水平间距", "调整对话框内分割线的左右缩进距离，范围：0-32dp", 0..32, "dp")
}


/**
 * 天空蓝主题专用 - 全局设置屏幕
 *
 * 此版本专为天空蓝主题设计，支持模糊效果设置等天空蓝主题特有功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SkyBlueGlobalSettingsScreen(
    settingsRepository: SettingsRepository,
    experimentalFeatureDetector: ExperimentalFeatureDetector? = null
) {
    val currentSettings by settingsRepository.globalSettings.collectAsState()

    // 小组件更新设置
    var widgetUpdateEnabled by remember(currentSettings) {
        mutableStateOf(currentSettings.widgetUpdateEnabled)
    }
    var widgetUpdateInterval by remember(currentSettings) {
        mutableStateOf(currentSettings.widgetUpdateInterval.toString())
    }


    // 上下文
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current

    // 主题管理器
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 模糊配置管理器
    val blurConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.BlurConfigurationManager.getInstance(context) }
    val blurConfig = blurConfigManager.getBlurConfiguration()

    // 卡片样式配置管理器
    val cardStyleConfigManager = remember { CardStyleConfigurationManager.getInstance(context, settingsRepository) }
    val cardStyleConfig = cardStyleConfigManager.getCardStyleConfiguration()

    // 页面布局配置管理器
    val pageLayoutConfigManager = remember { PageLayoutConfigurationManager.getInstance(context, settingsRepository) }
    val pageLayoutConfig = pageLayoutConfigManager.getPageLayoutConfiguration()

    // 天空蓝主题颜色配置管理器
    val colorConfigManager = remember { SkyBlueColorConfigurationManager.getInstance(context, settingsRepository) }
    val colorConfig = colorConfigManager.getSkyBlueColorConfiguration()

    // 底部导航栏样式配置管理器
    val bottomNavStyleConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.BottomNavigationStyleConfigurationManager.getInstance(context, settingsRepository) }
    val bottomNavStyleConfig = bottomNavStyleConfigManager.getBottomNavigationStyleConfiguration()

    // UI间距配置管理器
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 对话框间距配置管理器
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    // UI状态管理器
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 优化保护名单状态
    var protectionListApps by remember { mutableStateOf(emptyList<com.weinuo.quickcommands.model.SimpleAppInfo>()) }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    protectionListApps = loadedApps
                    // 保存到持久化存储
                    uiStateManager.saveAppListState("optimization_protection_list", "selected_apps", loadedApps)
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 初始化时加载保存的应用列表
    LaunchedEffect(Unit) {
        protectionListApps = uiStateManager.loadAppListState("optimization_protection_list", "selected_apps")
    }

    // 实验性功能状态
    var experimentalFeaturesEnabled by remember(currentSettings) {
        mutableStateOf(currentSettings.experimentalFeaturesEnabled)
    }

    // 体检结果延迟显示状态
    var checkupResultDelayEnabled by remember(currentSettings) {
        mutableStateOf(currentSettings.checkupResultDelayEnabled)
    }

    // 结果显示延迟时长状态
    var checkupDisplayDurationSeconds by remember(currentSettings) {
        mutableStateOf(currentSettings.checkupDisplayDurationSeconds.toString())
    }

    // 固定体检分数状态
    var fixedCheckupScoreEnabled by remember(currentSettings) {
        mutableStateOf(currentSettings.fixedCheckupScoreEnabled)
    }

    var fixedCheckupScore by remember(currentSettings) {
        mutableStateOf(currentSettings.fixedCheckupScore.toString())
    }

    // 导航项显示状态
    var navigationItemsVisibility by remember(currentSettings) {
        mutableStateOf(currentSettings.navigationItemsVisibility)
    }

    // 默认启动页面状态
    var defaultStartupPage by remember(currentSettings) {
        mutableStateOf(currentSettings.defaultStartupPage)
    }

    // 语言设置状态 - 使用局部状态管理，避免影响其他设置项
    var selectedLanguage by remember(currentSettings) {
        mutableStateOf(currentSettings.appLanguage)
    }

    // 语言显示名称 - 使用derivedStateOf减少重组
    val languageDisplayName by remember {
        derivedStateOf {
            when (selectedLanguage) {
                LanguageManager.LANGUAGE_SYSTEM -> context.withAppLanguage().getString(R.string.language_system_default)
                LanguageManager.LANGUAGE_CHINESE -> context.withAppLanguage().getString(R.string.language_chinese)
                LanguageManager.LANGUAGE_ENGLISH -> context.withAppLanguage().getString(R.string.language_english)
                else -> selectedLanguage
            }
        }
    }

    // 搜索框提示文字字重设置状态
    var selectedFontWeight by remember(currentSettings) {
        mutableStateOf(currentSettings.searchFieldPlaceholderFontWeight)
    }

    // 字重显示名称
    val fontWeightDisplayName by remember {
        derivedStateOf {
            when (selectedFontWeight) {
                "medium" -> context.withAppLanguage().getString(R.string.font_weight_medium)
                "regular" -> context.withAppLanguage().getString(R.string.font_weight_regular)
                else -> selectedFontWeight
            }
        }
    }

    // 搜索框图标粗细设置状态
    var selectedIconWeight by remember(currentSettings) {
        mutableStateOf(currentSettings.searchFieldIconWeight)
    }

    // 图标粗细显示名称
    val iconWeightDisplayName by remember {
        derivedStateOf {
            when (selectedIconWeight) {
                "regular" -> context.withAppLanguage().getString(R.string.icon_weight_regular)
                "medium" -> context.withAppLanguage().getString(R.string.icon_weight_medium)
                "bold" -> context.withAppLanguage().getString(R.string.icon_weight_bold)
                else -> selectedIconWeight
            }
        }
    }



    // 标题栏标题字重设置状态
    var selectedTitleFontWeight by remember(currentSettings) {
        mutableStateOf(currentSettings.topAppBarTitleFontWeight)
    }

    // 标题字重显示名称
    val titleFontWeightDisplayName by remember {
        derivedStateOf {
            when (selectedTitleFontWeight) {
                "normal" -> "常规"
                "medium" -> "中等"
                "bold" -> "粗体"
                else -> selectedTitleFontWeight
            }
        }
    }

    // 标题栏类型设置状态
    var selectedTopAppBarType by remember(currentSettings) {
        mutableStateOf(currentSettings.topAppBarType)
    }

    // 标题栏类型显示名称
    val topAppBarTypeDisplayName by remember {
        derivedStateOf {
            when (selectedTopAppBarType) {
                "standard" -> "标准顶部应用栏"
                "collapsible" -> "可折叠顶部应用栏"
                else -> selectedTopAppBarType
            }
        }
    }



    // 底部导航栏尺寸设置状态
    var bottomNavHeightText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavHeight.toString())
    }

    var bottomNavHorizontalPaddingText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavHorizontalPadding.toString())
    }

    var bottomNavVerticalPaddingText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavVerticalPadding.toString())
    }

    var bottomNavItemCornerRadiusText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavItemCornerRadius.toString())
    }

    var bottomNavItemOuterPaddingText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavItemOuterPadding.toString())
    }

    var bottomNavItemVerticalPaddingText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavItemVerticalPadding.toString())
    }

    var bottomNavItemHorizontalPaddingText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavItemHorizontalPadding.toString())
    }

    var bottomNavIconSizeText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavIconSize.toString())
    }

    var bottomNavIconTextSpacingText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavIconTextSpacing.toString())
    }

    var bottomNavTextFontSizeText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavTextFontSize.toString())
    }

    var bottomNavColorAnimationDurationText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavColorAnimationDuration.toString())
    }

    var bottomNavBackgroundAnimationDurationText by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavBackgroundAnimationDuration.toString())
    }

    // 底部导航栏字重设置状态
    var selectedBottomNavSelectedFontWeight by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavSelectedFontWeight)
    }

    var selectedBottomNavUnselectedFontWeight by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavUnselectedFontWeight)
    }

    var selectedBottomNavItemArrangement by remember(currentSettings) {
        mutableStateOf(currentSettings.bottomNavItemArrangement)
    }

    // 底部导航栏字重显示名称
    val bottomNavSelectedFontWeightDisplayName by remember {
        derivedStateOf {
            when (selectedBottomNavSelectedFontWeight) {
                "normal" -> "常规"
                "medium" -> "中等"
                "bold" -> "粗体"
                else -> selectedBottomNavSelectedFontWeight
            }
        }
    }

    val bottomNavUnselectedFontWeightDisplayName by remember {
        derivedStateOf {
            when (selectedBottomNavUnselectedFontWeight) {
                "normal" -> "常规"
                "medium" -> "中等"
                "bold" -> "粗体"
                else -> selectedBottomNavUnselectedFontWeight
            }
        }
    }

    val bottomNavItemArrangementDisplayName by remember {
        derivedStateOf {
            when (selectedBottomNavItemArrangement) {
                "spaceEvenly" -> "均匀分布"
                "spaceBetween" -> "两端对齐"
                "spaceAround" -> "环绕分布"
                else -> selectedBottomNavItemArrangement
            }
        }
    }

    // 卡片字重设置状态
    var selectedCardTitleFontWeight by remember(currentSettings) {
        mutableStateOf(currentSettings.cardTitleFontWeight)
    }

    var selectedCardContentFontWeight by remember(currentSettings) {
        mutableStateOf(currentSettings.cardContentFontWeight)
    }

    // 卡片字重显示名称
    val cardTitleFontWeightDisplayName by remember {
        derivedStateOf {
            when (selectedCardTitleFontWeight) {
                "normal" -> "常规"
                "medium" -> "中等"
                "bold" -> "粗体"
                else -> selectedCardTitleFontWeight
            }
        }
    }

    val cardContentFontWeightDisplayName by remember {
        derivedStateOf {
            when (selectedCardContentFontWeight) {
                "normal" -> "常规"
                "medium" -> "中等"
                "bold" -> "粗体"
                else -> selectedCardContentFontWeight
            }
        }
    }

    // 即时保存函数
    fun saveSettingsImmediately() {
        val newSettings = currentSettings.copy(
            widgetUpdateEnabled = widgetUpdateEnabled,
            widgetUpdateInterval = widgetUpdateInterval.toIntOrNull() ?: 24,
            experimentalFeaturesEnabled = experimentalFeaturesEnabled,
            appLanguage = selectedLanguage,
            searchFieldPlaceholderFontWeight = selectedFontWeight,
            searchFieldIconWeight = selectedIconWeight,
            topAppBarHeight = currentSettings.topAppBarHeight,
            topAppBarTitleFontSize = currentSettings.topAppBarTitleFontSize,
            topAppBarTitleFontWeight = selectedTitleFontWeight,
            topAppBarTitleVerticalOffset = currentSettings.topAppBarTitleVerticalOffset,
            topAppBarTitleHorizontalOffset = currentSettings.topAppBarTitleHorizontalOffset,
            topAppBarType = selectedTopAppBarType,
            // 底部导航栏尺寸设置
            bottomNavHeight = bottomNavHeightText.toIntOrNull() ?: 80,
            bottomNavHorizontalPadding = bottomNavHorizontalPaddingText.toIntOrNull() ?: 16,
            bottomNavVerticalPadding = bottomNavVerticalPaddingText.toIntOrNull() ?: 7,
            bottomNavItemCornerRadius = bottomNavItemCornerRadiusText.toIntOrNull() ?: 16,
            bottomNavItemOuterPadding = bottomNavItemOuterPaddingText.toIntOrNull() ?: 4,
            bottomNavItemVerticalPadding = bottomNavItemVerticalPaddingText.toIntOrNull() ?: 8,
            bottomNavItemHorizontalPadding = bottomNavItemHorizontalPaddingText.toIntOrNull() ?: 12,
            bottomNavIconSize = bottomNavIconSizeText.toIntOrNull() ?: 24,
            bottomNavIconTextSpacing = bottomNavIconTextSpacingText.toIntOrNull() ?: 4,
            bottomNavTextFontSize = bottomNavTextFontSizeText.toIntOrNull() ?: 11,
            bottomNavSelectedFontWeight = selectedBottomNavSelectedFontWeight,
            bottomNavUnselectedFontWeight = selectedBottomNavUnselectedFontWeight,
            bottomNavColorAnimationDuration = bottomNavColorAnimationDurationText.toIntOrNull() ?: 150,
            bottomNavBackgroundAnimationDuration = bottomNavBackgroundAnimationDurationText.toIntOrNull() ?: 150,
            bottomNavItemArrangement = selectedBottomNavItemArrangement,
            cardTitleFontWeight = selectedCardTitleFontWeight,
            cardContentFontWeight = selectedCardContentFontWeight,
            checkupResultDelayEnabled = checkupResultDelayEnabled,
            checkupDisplayDurationSeconds = checkupDisplayDurationSeconds.toIntOrNull() ?: 5,
            fixedCheckupScoreEnabled = fixedCheckupScoreEnabled,
            fixedCheckupScore = fixedCheckupScore.toIntOrNull() ?: 100,
            navigationItemsVisibility = navigationItemsVisibility,
            defaultStartupPage = defaultStartupPage
        )

        // 检查小组件设置是否有变化
        val widgetSettingsChanged = currentSettings.widgetUpdateEnabled != widgetUpdateEnabled ||
                currentSettings.widgetUpdateInterval != (widgetUpdateInterval.toIntOrNull() ?: 24)

        // 检查语言设置是否有变化
        val languageChanged = currentSettings.appLanguage != selectedLanguage

        // 批量保存所有设置，避免多次SharedPreferences写操作
        settingsRepository.saveGlobalSettings(newSettings)

        // 如果语言设置有变化，更新LanguageManager缓存并清理LocaleHelper缓存
        if (languageChanged) {
            LanguageManager.setLanguage(context, selectedLanguage)
            LocaleHelper.clearCache()
        }

        // 如果小组件设置有变化，立即更新小组件
        if (widgetSettingsChanged) {
            WidgetUpdateManager.forceUpdateAllWidgets(context)
        }
    }

    // 模糊强度设置对话框的状态变量（兼容性保留）
    var showBlurIntensityDialog by remember { mutableStateOf(false) }

    // 背景透明度设置对话框的状态变量（兼容性保留）
    var showBackgroundAlphaDialog by remember { mutableStateOf(false) }

    // 组件特定的模糊强度设置对话框状态变量
    var showTopBarBlurIntensityDialog by remember { mutableStateOf(false) }
    var showBottomBarBlurIntensityDialog by remember { mutableStateOf(false) }
    var showDialogBlurIntensityDialog by remember { mutableStateOf(false) }
    var showOverlayBlurIntensityDialog by remember { mutableStateOf(false) }

    // 组件特定的背景透明度设置对话框状态变量
    var showTopBarBackgroundAlphaDialog by remember { mutableStateOf(false) }
    var showBottomBarBackgroundAlphaDialog by remember { mutableStateOf(false) }
    var showDialogBackgroundAlphaDialog by remember { mutableStateOf(false) }
    var showOverlayBackgroundAlphaDialog by remember { mutableStateOf(false) }

    // 组件特定的模糊半径设置对话框状态变量
    var showTopBarBlurRadiusDialog by remember { mutableStateOf(false) }
    var showBottomBarBlurRadiusDialog by remember { mutableStateOf(false) }
    var showDialogBlurRadiusDialog by remember { mutableStateOf(false) }
    var showOverlayBlurRadiusDialog by remember { mutableStateOf(false) }

    // 组件特定的噪声因子设置对话框状态变量
    var showTopBarNoiseFactorDialog by remember { mutableStateOf(false) }
    var showBottomBarNoiseFactorDialog by remember { mutableStateOf(false) }
    var showDialogNoiseFactorDialog by remember { mutableStateOf(false) }
    var showOverlayNoiseFactorDialog by remember { mutableStateOf(false) }

    // 组件特定的色调强度设置对话框状态变量
    var showTopBarTintIntensityDialog by remember { mutableStateOf(false) }
    var showBottomBarTintIntensityDialog by remember { mutableStateOf(false) }
    var showDialogTintIntensityDialog by remember { mutableStateOf(false) }
    var showOverlayTintIntensityDialog by remember { mutableStateOf(false) }

    // 卡片样式设置对话框的状态变量
    var showCardStyleDialog by remember { mutableStateOf<CardStyleDialogType?>(null) }

    // 主题颜色设置对话框的状态变量
    var showThemeColorDialog by remember { mutableStateOf<ThemeColorDialogType?>(null) }

    // 底部导航栏样式设置对话框的状态变量
    var showBottomNavStyleDialog by remember { mutableStateOf<BottomNavStyleDialogType?>(null) }

    // 标题栏设置对话框的状态变量
    var showTopAppBarDialog by remember { mutableStateOf<TopAppBarDialogType?>(null) }

    // 标题栏按钮配置对话框的状态变量
    var showTopAppBarButtonDialog by remember { mutableStateOf<TopAppBarButtonDialogType?>(null) }

    // 页面布局设置对话框的状态变量
    var showPageLayoutDialog by remember { mutableStateOf<PageLayoutDialogType?>(null) }

    // UI间距设置对话框的状态变量
    var showUISpacingDialog by remember { mutableStateOf<UISpacingDialogType?>(null) }

    // 对话框间距设置对话框的状态变量
    var showDialogSpacingDialog by remember { mutableStateOf<DialogSpacingDialogType?>(null) }

    // 小组件更新间隔设置对话框的状态变量
    var showWidgetUpdateIntervalDialog by remember { mutableStateOf(false) }

    // 结果显示延迟时长设置对话框的状态变量
    var showCheckupDisplayDurationDialog by remember { mutableStateOf(false) }

    // 固定体检分数设置对话框的状态变量
    var showFixedCheckupScoreDialog by remember { mutableStateOf(false) }

    // 水波动效阈值设置对话框的状态变量
    var showWaterWaveThresholdDialog by remember { mutableStateOf(false) }

    // 导航项显示控制对话框的状态变量
    var showNavigationItemsVisibilityDialog by remember { mutableStateOf(false) }

    // 默认启动页面设置对话框的状态变量
    var showDefaultStartupPageDialog by remember { mutableStateOf(false) }

    // 计算TopAppBar高度，用于内容的初始顶部padding
    val density = LocalDensity.current
    val statusBarHeight = with(density) { WindowInsets.statusBars.getTop(density).toDp() }
    val topAppBarHeight = currentSettings.topAppBarHeight.dp + statusBarHeight // StandardTopAppBar高度

    // 创建LazyColumn滚动状态
    val lazyListState = rememberLazyListState()

    // 创建滚动行为 - 支持可折叠标题栏，只有在内容可滚动时才允许标题栏折叠
    val scrollBehavior = rememberIntegratedTopAppBarScrollBehavior(
        canScroll = {
            // 检查LazyColumn是否可以滚动：
            // 1. 有内容且可以向下滚动（不在顶部）
            // 2. 或者可以向上滚动（不在底部）
            lazyListState.canScrollBackward || lazyListState.canScrollForward
        }
    )

    // 配置TopAppBar - 支持可折叠类型
    SetIntegratedTopAppBar(
        config = TopAppBarConfig(
            title = stringResource(R.string.nav_global_settings),
            style = TopAppBarStyle.STANDARD,
            scrollBehavior = scrollBehavior, // 关键：添加滚动行为
            windowInsets = WindowInsets.statusBars
        )
    )

    // 注意：scrollBehavior已在上面创建，不需要从topAppBarConfig获取

    // 动态计算顶部padding - 根据标题栏类型优化内容定位
    val topPadding = remember(currentSettings.topAppBarType, statusBarHeight, topAppBarHeight) {
        if (currentSettings.topAppBarType == "collapsible") {
            // 可折叠模式：使用展开状态高度，确保内容不被遮挡
            152.dp + statusBarHeight + 8.dp
        } else {
            // 标准模式：使用固定高度
            topAppBarHeight + 8.dp
        }
    }

    // 直接返回页面内容，TopAppBar由IntegratedMainLayout处理
    Box(modifier = Modifier.fillMaxSize()) {
        LazyColumn(
            state = lazyListState,
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(scrollBehavior.nestedScrollConnection),
            contentPadding = PaddingValues(
                start = 16.dp,
                end = 16.dp,
                top = topPadding,
                bottom = 88.dp
            ),
            verticalArrangement = Arrangement.spacedBy(uiSpacingConfig.globalSettingsItemSpacing.dp)
        ) {
                // 外观主题设置
                // 设置卡片
                item {
                ThemedCard(
                    onClick = null, // 明确设置为null，避免双重点击效果
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 主题选择下拉菜单
                    ThemeDropdownMenu(
                        uiSpacingConfig = uiSpacingConfig
                    )
                }
                }
                // 模糊设置分组标题
                // 设置标题
                item {
                Text(
                    text = "模糊设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 模糊效果设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 顶部栏模糊开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    blurConfigManager.updateTopBarBlur(!blurConfig.topBarBlurEnabled)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "顶部栏模糊效果",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = blurConfig.topBarBlurEnabled,
                                onCheckedChange = { enabled ->
                                    blurConfigManager.updateTopBarBlur(enabled)
                                }
                            )
                        }
                        // 顶部栏模糊设置（仅在启用模糊时显示）
                        if (blurConfig.topBarBlurEnabled) {
                            // 分割线
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )

                            // 顶部栏模糊样式选择
                            BlurStyleDropdownMenu(
                                title = "顶部栏模糊样式",
                                selectedBlurStyle = blurConfig.getComponentBlurStyle(BlurComponent.TOP_BAR),
                                blurStyleDisplayName = when (blurConfig.getComponentBlurStyle(BlurComponent.TOP_BAR)) {
                                    "preset" -> "预设材质"
                                    "custom" -> "自定义"
                                    else -> "预设材质"
                                },
                                onBlurStyleSelected = { style ->
                                    blurConfigManager.updateComponentBlurStyle(BlurComponent.TOP_BAR, style)
                                },
                                uiSpacingConfig = uiSpacingConfig
                            )

                            // 根据模糊样式显示不同的设置项
                            when (blurConfig.getComponentBlurStyle(BlurComponent.TOP_BAR)) {
                                "preset" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 预设材质选择
                                    PresetMaterialDropdownMenu(
                                        title = "顶部栏材质类型",
                                        selectedMaterial = blurConfig.getComponentPresetMaterial(BlurComponent.TOP_BAR),
                                        materialDisplayName = when (blurConfig.getComponentPresetMaterial(BlurComponent.TOP_BAR)) {
                                            "ultraThin" -> "超薄"
                                            "thin" -> "薄"
                                            "regular" -> "常规"
                                            "thick" -> "厚"
                                            else -> "常规"
                                        },
                                        onMaterialSelected = { material ->
                                            blurConfigManager.updateComponentPresetMaterial(BlurComponent.TOP_BAR, material)
                                        },
                                        uiSpacingConfig = uiSpacingConfig
                                    )
                                }
                                "custom" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showTopBarBlurIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "顶部栏模糊强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentIntensityPercentage(BlurComponent.TOP_BAR)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 顶部栏背景透明度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showTopBarBackgroundAlphaDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "顶部栏背景透明度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.TOP_BAR)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊半径设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showTopBarBlurRadiusDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "顶部栏模糊半径",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBlurRadiusValue(BlurComponent.TOP_BAR)}dp",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义噪声因子设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showTopBarNoiseFactorDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "顶部栏噪声因子",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.2f", blurConfig.getComponentNoiseFactor(BlurComponent.TOP_BAR)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义色调强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showTopBarTintIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "顶部栏色调强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.1f", blurConfig.getComponentTintIntensity(BlurComponent.TOP_BAR)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 底部栏模糊开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    blurConfigManager.updateBottomBarBlur(!blurConfig.bottomBarBlurEnabled)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "底部栏模糊效果",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = blurConfig.bottomBarBlurEnabled,
                                onCheckedChange = { enabled ->
                                    blurConfigManager.updateBottomBarBlur(enabled)
                                }
                            )
                        }
                        // 底部栏模糊设置（仅在启用模糊时显示）
                        if (blurConfig.bottomBarBlurEnabled) {
                            // 分割线
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )

                            // 底部栏模糊样式选择
                            BlurStyleDropdownMenu(
                                title = "底部栏模糊样式",
                                selectedBlurStyle = blurConfig.getComponentBlurStyle(BlurComponent.BOTTOM_BAR),
                                blurStyleDisplayName = when (blurConfig.getComponentBlurStyle(BlurComponent.BOTTOM_BAR)) {
                                    "preset" -> "预设材质"
                                    "custom" -> "自定义"
                                    else -> "预设材质"
                                },
                                onBlurStyleSelected = { style ->
                                    blurConfigManager.updateComponentBlurStyle(BlurComponent.BOTTOM_BAR, style)
                                },
                                uiSpacingConfig = uiSpacingConfig
                            )

                            // 根据模糊样式显示不同的设置项
                            when (blurConfig.getComponentBlurStyle(BlurComponent.BOTTOM_BAR)) {
                                "preset" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 预设材质选择
                                    PresetMaterialDropdownMenu(
                                        title = "底部栏材质类型",
                                        selectedMaterial = blurConfig.getComponentPresetMaterial(BlurComponent.BOTTOM_BAR),
                                        materialDisplayName = when (blurConfig.getComponentPresetMaterial(BlurComponent.BOTTOM_BAR)) {
                                            "ultraThin" -> "超薄"
                                            "thin" -> "薄"
                                            "regular" -> "常规"
                                            "thick" -> "厚"
                                            else -> "常规"
                                        },
                                        onMaterialSelected = { material ->
                                            blurConfigManager.updateComponentPresetMaterial(BlurComponent.BOTTOM_BAR, material)
                                        },
                                        uiSpacingConfig = uiSpacingConfig
                                    )
                                }
                                "custom" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showBottomBarBlurIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "底部栏模糊强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentIntensityPercentage(BlurComponent.BOTTOM_BAR)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 底部栏背景透明度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showBottomBarBackgroundAlphaDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "底部栏背景透明度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.BOTTOM_BAR)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊半径设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showBottomBarBlurRadiusDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "底部栏模糊半径",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBlurRadiusValue(BlurComponent.BOTTOM_BAR)}dp",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义噪声因子设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showBottomBarNoiseFactorDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "底部栏噪声因子",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.2f", blurConfig.getComponentNoiseFactor(BlurComponent.BOTTOM_BAR)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义色调强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showBottomBarTintIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "底部栏色调强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.1f", blurConfig.getComponentTintIntensity(BlurComponent.BOTTOM_BAR)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 对话框模糊开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    blurConfigManager.updateDialogBlur(!blurConfig.dialogBlurEnabled)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "对话框模糊效果",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = blurConfig.dialogBlurEnabled,
                                onCheckedChange = { enabled ->
                                    blurConfigManager.updateDialogBlur(enabled)
                                }
                            )
                        }
                        // 对话框模糊样式设置（仅在启用模糊时显示）
                        if (blurConfig.dialogBlurEnabled) {
                            // 分割线
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )

                            // 对话框模糊样式选择
                            BlurStyleDropdownMenu(
                                title = "对话框模糊样式",
                                selectedBlurStyle = blurConfig.getComponentBlurStyle(BlurComponent.DIALOG),
                                blurStyleDisplayName = when (blurConfig.getComponentBlurStyle(BlurComponent.DIALOG)) {
                                    "preset" -> "预设材质"
                                    "custom" -> "自定义"
                                    else -> "预设材质"
                                },
                                onBlurStyleSelected = { style ->
                                    blurConfigManager.updateComponentBlurStyle(BlurComponent.DIALOG, style)
                                },
                                uiSpacingConfig = uiSpacingConfig
                            )

                            // 根据模糊样式显示不同的设置项
                            when (blurConfig.getComponentBlurStyle(BlurComponent.DIALOG)) {
                                "preset" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 预设材质选择
                                    PresetMaterialDropdownMenu(
                                        title = "对话框材质类型",
                                        selectedMaterial = blurConfig.getComponentPresetMaterial(BlurComponent.DIALOG),
                                        materialDisplayName = when (blurConfig.getComponentPresetMaterial(BlurComponent.DIALOG)) {
                                            "ultraThin" -> "超薄"
                                            "thin" -> "薄"
                                            "regular" -> "常规"
                                            "thick" -> "厚"
                                            else -> "常规"
                                        },
                                        onMaterialSelected = { material ->
                                            blurConfigManager.updateComponentPresetMaterial(BlurComponent.DIALOG, material)
                                        },
                                        uiSpacingConfig = uiSpacingConfig
                                    )
                                }
                                "custom" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showDialogBlurIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "对话框模糊强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentIntensityPercentage(BlurComponent.DIALOG)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 对话框背景透明度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showDialogBackgroundAlphaDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "对话框背景透明度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.DIALOG)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊半径设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showDialogBlurRadiusDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "对话框模糊半径",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBlurRadiusValue(BlurComponent.DIALOG)}dp",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义噪声因子设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showDialogNoiseFactorDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "对话框噪声因子",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.2f", blurConfig.getComponentNoiseFactor(BlurComponent.DIALOG)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义色调强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showDialogTintIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "对话框色调强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.1f", blurConfig.getComponentTintIntensity(BlurComponent.DIALOG)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 覆盖层模糊开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    blurConfigManager.updateOverlayBlur(!blurConfig.overlayBlurEnabled)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "覆盖层模糊效果",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = blurConfig.overlayBlurEnabled,
                                onCheckedChange = { enabled ->
                                    blurConfigManager.updateOverlayBlur(enabled)
                                }
                            )
                        }
                        // 覆盖层模糊样式设置（仅在启用模糊时显示）
                        if (blurConfig.overlayBlurEnabled) {
                            // 分割线
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )

                            // 覆盖层模糊样式选择
                            BlurStyleDropdownMenu(
                                title = "覆盖层模糊样式",
                                selectedBlurStyle = blurConfig.getComponentBlurStyle(BlurComponent.OVERLAY),
                                blurStyleDisplayName = when (blurConfig.getComponentBlurStyle(BlurComponent.OVERLAY)) {
                                    "preset" -> "预设材质"
                                    "custom" -> "自定义"
                                    else -> "预设材质"
                                },
                                onBlurStyleSelected = { style ->
                                    blurConfigManager.updateComponentBlurStyle(BlurComponent.OVERLAY, style)
                                },
                                uiSpacingConfig = uiSpacingConfig
                            )

                            // 根据模糊样式显示不同的设置项
                            when (blurConfig.getComponentBlurStyle(BlurComponent.OVERLAY)) {
                                "preset" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 预设材质选择
                                    PresetMaterialDropdownMenu(
                                        title = "覆盖层材质类型",
                                        selectedMaterial = blurConfig.getComponentPresetMaterial(BlurComponent.OVERLAY),
                                        materialDisplayName = when (blurConfig.getComponentPresetMaterial(BlurComponent.OVERLAY)) {
                                            "ultraThin" -> "超薄"
                                            "thin" -> "薄"
                                            "regular" -> "常规"
                                            "thick" -> "厚"
                                            else -> "常规"
                                        },
                                        onMaterialSelected = { material ->
                                            blurConfigManager.updateComponentPresetMaterial(BlurComponent.OVERLAY, material)
                                        },
                                        uiSpacingConfig = uiSpacingConfig
                                    )
                                }
                                "custom" -> {
                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showOverlayBlurIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "覆盖层模糊强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentIntensityPercentage(BlurComponent.OVERLAY)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 覆盖层背景透明度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showOverlayBackgroundAlphaDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "覆盖层背景透明度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.OVERLAY)}%",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义模糊半径设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showOverlayBlurRadiusDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "覆盖层模糊半径",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = "${blurConfig.getComponentBlurRadiusValue(BlurComponent.OVERLAY)}dp",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义噪声因子设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showOverlayNoiseFactorDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "覆盖层噪声因子",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.2f", blurConfig.getComponentNoiseFactor(BlurComponent.OVERLAY)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    // 分割线
                                    themeContext.componentFactory.createDivider()(
                                        DividerConfig(
                                            modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                        )
                                    )

                                    // 自定义色调强度设置
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable {
                                                showOverlayTintIntensityDialog = true
                                            }
                                            .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "覆盖层色调强度",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurface,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                            modifier = Modifier.weight(1f)
                                        )
                                        Text(
                                            text = String.format("%.1f", blurConfig.getComponentTintIntensity(BlurComponent.OVERLAY)),
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                }

                // 功能设置分组标题
                // 设置标题
                item {
                Text(
                    text = "功能设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }

                // 悬浮加速球设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 悬浮加速球开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    val newSettings = currentSettings.copy(floatingAcceleratorEnabled = !currentSettings.floatingAcceleratorEnabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "悬浮加速球",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = currentSettings.floatingAcceleratorEnabled,
                                onCheckedChange = { enabled ->
                                    val newSettings = currentSettings.copy(floatingAcceleratorEnabled = enabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                            )
                        }

                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 优化保护名单设置
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    // 跳转到应用选择界面
                                    val resultKey = "optimization_protection_list_${System.currentTimeMillis()}"
                                    val intent = AppSelectionActivity.createMultiSelectionIntent(
                                        context = context,
                                        selectedAppPackageNames = protectionListApps.map { it.packageName },
                                        resultKey = resultKey
                                    )
                                    appSelectionLauncher.launch(intent)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "优化保护名单",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            Text(
                                text = if (protectionListApps.isEmpty()) {
                                    "未选择应用"
                                } else {
                                    "已选择${protectionListApps.size}个应用"
                                },
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Icon(
                                imageVector = Icons.Default.ChevronRight,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                }
                }

                // 水球设置分组标题
                // 设置标题
                item {
                Text(
                    text = "水球设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }

                // 水球设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 水球高级材质开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    val newSettings = currentSettings.copy(waterBallAdvancedMaterialEnabled = !currentSettings.waterBallAdvancedMaterialEnabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "水球高级材质",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = currentSettings.waterBallAdvancedMaterialEnabled,
                                onCheckedChange = { enabled ->
                                    val newSettings = currentSettings.copy(waterBallAdvancedMaterialEnabled = enabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                            )
                        }

                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 水波动效预留空间开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    val newSettings = currentSettings.copy(waterWaveReserveSpaceEnabled = !currentSettings.waterWaveReserveSpaceEnabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "水波动效预留空间",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = currentSettings.waterWaveReserveSpaceEnabled,
                                onCheckedChange = { enabled ->
                                    val newSettings = currentSettings.copy(waterWaveReserveSpaceEnabled = enabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                            )
                        }

                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 水球体感跟随开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    val newSettings = currentSettings.copy(waterBallMotionSensingEnabled = !currentSettings.waterBallMotionSensingEnabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column(
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    text = "水球体感跟随",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
                                )
                                Text(
                                    text = "水球水面随手机倾斜而倾斜",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    modifier = Modifier.padding(top = 2.dp)
                                )
                            }
                            SkyBlueSwitch(
                                checked = currentSettings.waterBallMotionSensingEnabled,
                                onCheckedChange = { enabled ->
                                    val newSettings = currentSettings.copy(waterBallMotionSensingEnabled = enabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                            )
                        }

                        // 水波动效阈值设置项（仅在开启预留空间时显示）
                        if (currentSettings.waterWaveReserveSpaceEnabled) {
                            // 分割线 - 使用主题感知的分割线组件
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        showWaterWaveThresholdDialog = true
                                    }
                                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "水位停止阈值",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )
                                Text(
                                    text = "${currentSettings.waterWaveStopThreshold}分",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
                }

                // 卡片设置分组标题
                // 设置标题
                item {
                Text(
                    text = "卡片设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 卡片样式设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 圆角大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.CORNER_RADIUS
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "圆角大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前圆角大小值
                            Text(
                                text = "${currentSettings.cardCornerRadius}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 水平内边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.HORIZONTAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "水平内边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前水平内边距值
                            Text(
                                text = "${currentSettings.cardDefaultHorizontalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 内容卡片垂直边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.VERTICAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "内容卡片垂直边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前内容卡片垂直边距值
                            Text(
                                text = "${currentSettings.cardDefaultVerticalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 设置卡片垂直边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.SETTINGS_VERTICAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "设置卡片垂直边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前设置卡片垂直边距值
                            Text(
                                text = "${currentSettings.cardSettingsVerticalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 项目间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.ITEM_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "卡片间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前卡片间距值
                            Text(
                                text = "${currentSettings.cardItemSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 区域间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.SECTION_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "区域间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前区域间距值
                            Text(
                                text = "${currentSettings.cardSectionSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 卡片内容垂直间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.CONTENT_VERTICAL_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "卡片内容垂直间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前卡片内容垂直间距值
                            Text(
                                text = "${currentSettings.cardContentVerticalSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 卡片内容水平间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.CONTENT_HORIZONTAL_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "卡片内容水平间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前内容水平间距值
                            Text(
                                text = "${currentSettings.cardContentHorizontalSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 选中边框宽度设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.SELECTED_BORDER_WIDTH
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "选中边框宽度",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前选中边框宽度值
                            Text(
                                text = "${currentSettings.cardSelectedBorderWidth}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 指令名称字体大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.CARD_TITLE_FONT_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "卡片标题字体大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前指令名称字体大小值
                            Text(
                                text = "${currentSettings.cardTitleFontSize}sp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 卡片标题字重设置行
                        FontWeightDropdownMenu(
                            title = "卡片标题字重",
                            selectedFontWeight = selectedCardTitleFontWeight,
                            fontWeightDisplayName = cardTitleFontWeightDisplayName,
                            onFontWeightSelected = { fontWeight ->
                                selectedCardTitleFontWeight = fontWeight
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 功能描述字体大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.CARD_CONTENT_FONT_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "卡片描述字体大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前功能描述字体大小值
                            Text(
                                text = "${currentSettings.cardContentFontSize}sp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 卡片描述字重设置行
                        FontWeightDropdownMenu(
                            title = "卡片描述字重",
                            selectedFontWeight = selectedCardContentFontWeight,
                            fontWeightDisplayName = cardContentFontWeightDisplayName,
                            onFontWeightSelected = { fontWeight ->
                                selectedCardContentFontWeight = fontWeight
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 快捷指令图标大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showCardStyleDialog = CardStyleDialogType.CARD_ICON_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "快捷指令图标大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前图标大小值
                            Text(
                                text = "${currentSettings.cardIconSize}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 重置默认设置行 - 与其他设置项保持一致的样式
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    cardStyleConfigManager.resetToDefaults()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "重置默认",
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color(0xFF0A59F7), // 保持蓝色
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                }
                // 页面布局设置分组标题
                // 设置标题
                item {
                Text(
                    text = "页面布局设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 页面布局设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 页面内容水平边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showPageLayoutDialog = PageLayoutDialogType.CONTENT_HORIZONTAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "页面水平边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前页面水平边距值
                            Text(
                                text = "${currentSettings.pageContentHorizontalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                            // 分割线 - 使用主题感知的分割线组件
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )
                        // 搜索框外边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showPageLayoutDialog = PageLayoutDialogType.SEARCH_FIELD_MARGIN
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "搜索框边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前搜索框边距值
                            Text(
                                text = "${currentSettings.pageSearchFieldMargin}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                            // 分割线 - 使用主题感知的分割线组件
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )
                        // 页面底部边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showPageLayoutDialog = PageLayoutDialogType.BOTTOM_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "页面底部边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前页面底部边距值
                            Text(
                                text = "${currentSettings.pageBottomPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                            // 分割线 - 使用主题感知的分割线组件
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )
                        // 页面标题间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showPageLayoutDialog = PageLayoutDialogType.HEADER_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "页面标题间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前页面标题间距值
                            Text(
                                text = "${currentSettings.pageHeaderSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                            // 分割线 - 使用主题感知的分割线组件
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )
                        // 滚动内容间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showPageLayoutDialog = PageLayoutDialogType.SCROLL_CONTENT_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "滚动内容间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前滚动内容间距值
                            Text(
                                text = "${currentSettings.pageScrollContentSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                            // 分割线 - 使用主题感知的分割线组件
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )
                        // 重置默认设置行 - 与其他设置项保持一致的样式
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    pageLayoutConfigManager.resetToDefaults()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "重置默认",
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color(0xFF0A59F7), // 保持蓝色
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                }
                // 界面间距设置标题
                // 设置标题
                item {
                Text(
                    text = "界面间距设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // UI间距设置卡片
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 设置项垂直间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_ITEM_VERTICAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "设置项垂直间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前设置项垂直间距值
                            Text(
                                text = "${uiSpacingConfig.settingsItemVerticalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 分割线显示开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    uiSpacingConfigManager.updateDividerVisible(!uiSpacingConfig.dividerVisible)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "显示分割线",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    fontWeight = FontWeight.Medium
                                )
                                Text(
                                    text = "控制设置项之间的分割线是否显示",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                            SkyBlueSwitch(
                                checked = uiSpacingConfig.dividerVisible,
                                onCheckedChange = { visible ->
                                    uiSpacingConfigManager.updateDividerVisible(visible)
                                }
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 分割线水平间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.DIVIDER_HORIZONTAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "分割线水平间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前分割线水平间距值
                            Text(
                                text = "${uiSpacingConfig.dividerHorizontalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 设置卡片内边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_CARD_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "设置卡片内边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前设置卡片内边距值
                            Text(
                                text = "${uiSpacingConfig.settingsCardPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 设置项间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_ITEM_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "设置项间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前设置项间距值
                            Text(
                                text = "${uiSpacingConfig.settingsItemSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 设置标题间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_TITLE_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "设置标题间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前设置标题间距值
                            Text(
                                text = "${uiSpacingConfig.settingsTitleSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 设置描述间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_DESCRIPTION_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "设置描述间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前设置描述间距值
                            Text(
                                text = "${uiSpacingConfig.settingsDescriptionSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 设置分组标题水平间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_GROUP_TITLE_HORIZONTAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "分组标题水平间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前分组标题水平间距值
                            Text(
                                text = "${uiSpacingConfig.settingsGroupTitleHorizontalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 设置分组标题上边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_GROUP_TITLE_TOP_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "分组标题上边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前分组标题上边距值
                            Text(
                                text = "${uiSpacingConfig.settingsGroupTitleTopPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 设置分组标题下边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.SETTINGS_GROUP_TITLE_BOTTOM_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "分组标题下边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前分组标题下边距值
                            Text(
                                text = "${uiSpacingConfig.settingsGroupTitleBottomPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 全局设置界面间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showUISpacingDialog = UISpacingDialogType.GLOBAL_SETTINGS_ITEM_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "全局设置界面间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前全局设置界面间距值
                            Text(
                                text = "${uiSpacingConfig.globalSettingsItemSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 重置默认设置行 - 与其他设置项保持一致的样式
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    uiSpacingConfigManager.resetToDefaults()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "重置默认",
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color(0xFF0A59F7), // 保持蓝色
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                }
                // 对话框间距设置分组标题
                // 设置标题
                item {
                Text(
                    text = "对话框间距设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 对话框间距设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 对话框外边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.OUTER_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "对话框外边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前对话框外边距值
                            Text(
                                text = "${currentSettings.dialogOuterPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 图标下方间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.ICON_BOTTOM_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "图标下方间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前图标下方间距值
                            Text(
                                text = "${currentSettings.dialogIconBottomPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题下方间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.TITLE_BOTTOM_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "标题下方间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前标题下方间距值
                            Text(
                                text = "${currentSettings.dialogTitleBottomPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 内容下方间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.CONTENT_BOTTOM_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "内容下方间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前内容下方间距值
                            Text(
                                text = "${currentSettings.dialogContentBottomPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 按钮上方间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.BUTTON_TOP_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "按钮上方间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前按钮上方间距值
                            Text(
                                text = "${currentSettings.dialogButtonTopPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 按钮下方间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.BUTTON_BOTTOM_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "按钮下方间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前按钮下方间距值
                            Text(
                                text = "${currentSettings.dialogButtonBottomPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题字体大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.TITLE_FONT_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "标题字体大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前标题字体大小值
                            Text(
                                text = "${currentSettings.dialogTitleFontSize}sp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 分割线水平间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDialogSpacingDialog = DialogSpacingDialogType.DIVIDER_HORIZONTAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "分割线水平间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前分割线水平间距值
                            Text(
                                text = "${currentSettings.dialogDividerHorizontalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 重置默认设置行 - 与其他设置项保持一致的样式
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    dialogSpacingConfigManager.resetToDefaults()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "重置默认",
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color(0xFF0A59F7), // 保持蓝色
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                }
                // 标题栏按钮配置分组标题
                // 设置标题
                item {
                Text(
                    text = "标题栏按钮配置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 标题栏按钮配置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 按钮背景显示开关设置行
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    val newSettings = currentSettings.copy(topAppBarButtonCircleBackgroundEnabled = !currentSettings.topAppBarButtonCircleBackgroundEnabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "按钮背景显示",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = currentSettings.topAppBarButtonCircleBackgroundEnabled,
                                onCheckedChange = { enabled ->
                                    val newSettings = currentSettings.copy(topAppBarButtonCircleBackgroundEnabled = enabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                            )
                        }

                        // 分割线
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 按钮背景尺寸设置行
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showTopAppBarButtonDialog = TopAppBarButtonDialogType.CIRCLE_BACKGROUND_SIZE }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "按钮背景尺寸",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            Text(
                                text = "${currentSettings.topAppBarButtonCircleBackgroundSize}dp",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                        // 分割线
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 按钮边缘间距设置行
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showTopAppBarButtonDialog = TopAppBarButtonDialogType.CIRCLE_BACKGROUND_HORIZONTAL_MARGIN }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "按钮边缘间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            Text(
                                text = "${currentSettings.topAppBarButtonCircleBackgroundHorizontalMargin}dp",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                        // 分割线
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 按钮标题间距设置行
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { showTopAppBarButtonDialog = TopAppBarButtonDialogType.CIRCLE_BACKGROUND_RIGHT_MARGIN }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "按钮标题间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            Text(
                                text = "${currentSettings.topAppBarButtonCircleBackgroundRightMargin}dp",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                    }
                }
                }
                // 底部导航栏样式设置分组标题
                // 设置标题
                item {
                Text(
                    text = "底部导航栏样式设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 底部导航栏样式设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 导航栏高度设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.HEIGHT
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航栏高度",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前导航栏高度值
                            Text(
                                text = "${currentSettings.bottomNavHeight}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 水平内边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.HORIZONTAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "水平内边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前水平内边距值
                            Text(
                                text = "${currentSettings.bottomNavHorizontalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 垂直内边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.VERTICAL_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "垂直内边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前垂直内边距值
                            Text(
                                text = "${currentSettings.bottomNavVerticalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 导航项圆角设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.ITEM_CORNER_RADIUS
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航项圆角",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前导航项圆角值
                            Text(
                                text = "${currentSettings.bottomNavItemCornerRadius}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 导航项外边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.ITEM_OUTER_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航项外边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前导航项外边距值
                            Text(
                                text = "${currentSettings.bottomNavItemOuterPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 导航项内边距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.ITEM_INNER_PADDING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航项内边距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前导航项内边距值
                            Text(
                                text = "${currentSettings.bottomNavItemVerticalPadding}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 图标大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.ICON_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "图标大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前图标大小值
                            Text(
                                text = "${currentSettings.bottomNavIconSize}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 图标文字间距设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.ICON_TEXT_SPACING
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "图标文字间距",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前图标文字间距值
                            Text(
                                text = "${currentSettings.bottomNavIconTextSpacing}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 文字大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showBottomNavStyleDialog = BottomNavStyleDialogType.TEXT_FONT_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "文字大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前文字大小值
                            Text(
                                text = "${currentSettings.bottomNavTextFontSize}sp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 选中状态字重设置行 - 与其他设置项保持一致的简洁样式
                        FontWeightDropdownMenu(
                            title = "选中状态字重",
                            selectedFontWeight = selectedBottomNavSelectedFontWeight,
                            fontWeightDisplayName = bottomNavSelectedFontWeightDisplayName,
                            onFontWeightSelected = { fontWeight ->
                                selectedBottomNavSelectedFontWeight = fontWeight
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 未选中状态字重设置行 - 与其他设置项保持一致的简洁样式
                        FontWeightDropdownMenu(
                            title = "未选中状态字重",
                            selectedFontWeight = selectedBottomNavUnselectedFontWeight,
                            fontWeightDisplayName = bottomNavUnselectedFontWeightDisplayName,
                            onFontWeightSelected = { fontWeight ->
                                selectedBottomNavUnselectedFontWeight = fontWeight
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 导航项排列方式设置行 - 与其他设置项保持一致的简洁样式
                        BottomNavArrangementDropdownMenu(
                            selectedArrangement = selectedBottomNavItemArrangement,
                            arrangementDisplayName = bottomNavItemArrangementDisplayName,
                            onArrangementSelected = { arrangement ->
                                selectedBottomNavItemArrangement = arrangement
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 重置默认设置行 - 与其他设置项保持一致的样式
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    bottomNavStyleConfigManager.resetToDefaults()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "重置默认",
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color(0xFF0A59F7), // 保持蓝色
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                }
                // 主题颜色设置分组标题
                // 设置标题
                item {
                Text(
                    text = "主题颜色设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 主题颜色设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 品牌主色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.PRIMARY
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "品牌主色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBluePrimary,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 主色上的文本设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ON_PRIMARY
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "主色上的文本",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueOnPrimary,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 主色容器设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.PRIMARY_CONTAINER
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "主色容器",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBluePrimaryContainer,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 主色容器上的文本设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ON_PRIMARY_CONTAINER
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "主色容器上的文本",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueOnPrimaryContainer,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 次要色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.SECONDARY
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "次要色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueSecondary,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 次要色上的文本设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ON_SECONDARY
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "次要色上的文本",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueOnSecondary,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 次要色容器设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.SECONDARY_CONTAINER
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "次要色容器",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueSecondaryContainer,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 次要色容器上的文本设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ON_SECONDARY_CONTAINER
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "次要色容器上的文本",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueOnSecondaryContainer,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 背景色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.BACKGROUND
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "背景色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueBackground,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 背景上的文本设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ON_BACKGROUND
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "背景上的文本",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueOnBackground,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 表面色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.SURFACE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "表面色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueSurface,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 表面上的文本设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ON_SURFACE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "表面上的文本",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueOnSurface,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 确认色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.CONFIRM
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "确认色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueConfirm,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 强调文本色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.FONT_EMPHASIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "强调文本色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueFontEmphasize,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 强调图标色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ICON_EMPHASIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "强调图标色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueIconEmphasize,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 辅助强调图标色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.ICON_SUB_EMPHASIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "辅助强调图标色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueIconSubEmphasize,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 强调背景色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.BACKGROUND_EMPHASIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "强调背景色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueBackgroundEmphasize,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 焦点背景色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.BACKGROUND_FOCUS
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "焦点背景色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueBackgroundFocus,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 导航栏背景色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.BOTTOM_NAV_BACKGROUND
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航栏背景色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueBottomNavBackground,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 导航栏选中图标颜色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.BOTTOM_NAV_SELECTED_ICON
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航栏选中图标颜色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueBottomNavSelectedIcon,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 导航栏未选中图标颜色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.BOTTOM_NAV_UNSELECTED_ICON
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航栏未选中图标颜色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueBottomNavUnselectedIcon,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题栏背景色设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showThemeColorDialog = ThemeColorDialogType.TOP_BAR_BACKGROUND
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "标题栏背景色",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前颜色值
                            Text(
                                text = currentSettings.skyBlueTopBarBackground,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 重置默认设置行 - 与其他设置项保持一致的样式
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    colorConfigManager.resetToDefaults()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "重置默认",
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color(0xFF0A59F7), // 保持蓝色
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                }
                // 小组件更新设置标题
                // 设置标题
                item {
                Text(
                    text = stringResource(R.string.widget_update_settings),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 小组件更新设置卡片
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 启用小组件更新设置行
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    // 小组件更新设置无需Shizuku权限，直接执行并立即保存
                                    widgetUpdateEnabled = !widgetUpdateEnabled
                                    saveSettingsImmediately()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = stringResource(R.string.widget_update_enabled),
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = widgetUpdateEnabled,
                                onCheckedChange = {
                                    // 小组件更新设置无需Shizuku权限，直接执行并立即保存
                                    widgetUpdateEnabled = it
                                    saveSettingsImmediately()
                                }
                            )
                        }
                        // 只有当小组件更新启用时才显示更新间隔设置
                        if (widgetUpdateEnabled) {
                            // 分割线 - 使用主题感知的分割线组件
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )
                            // 更新间隔设置行 - 点击弹出对话框
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        showWidgetUpdateIntervalDialog = true
                                    }
                                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = stringResource(R.string.widget_update_interval),
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )
                                // 显示当前更新间隔值
                                Text(
                                    text = "${widgetUpdateInterval}小时",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 立即更新小组件行 - 与其他设置项保持一致的样式
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    WidgetUpdateManager.forceUpdateAllWidgets(context)
                                    Toast.makeText(context, context.getString(R.string.widget_update_completed), Toast.LENGTH_SHORT).show()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "立即更新小组件",
                                style = MaterialTheme.typography.bodyLarge,
                                color = Color(0xFF0A59F7), // 保持蓝色
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
                }
                // 语言设置卡片
                // 语言设置分组标题
                // 设置标题
                item {
                Text(
                    text = stringResource(R.string.language_settings),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 语言设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 语言选择设置行 - 使用下拉菜单组件
                        LanguageDropdownMenu(
                            title = stringResource(R.string.language_selection_title),
                            selectedLanguage = selectedLanguage,
                            languageDisplayName = languageDisplayName,
                            onLanguageSelected = { language ->
                                val previousLanguage = selectedLanguage
                                selectedLanguage = language
                                saveSettingsImmediately()
                                // 如果语言确实发生了变化，显示重启提示
                                if (previousLanguage != language) {
                                    Toast.makeText(
                                        context,
                                        "语言设置已保存，重启应用后生效",
                                        Toast.LENGTH_LONG
                                    ).show()
                                }
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                    }
                }
                }
                // 搜索框设置分组标题
                // 设置标题
                item {
                Text(
                    text = stringResource(R.string.search_field_settings),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 搜索框设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 提示文字字重设置行
                        FontWeightDropdownMenu(
                            title = stringResource(R.string.search_field_placeholder_font_weight),
                            selectedFontWeight = selectedFontWeight,
                            fontWeightDisplayName = fontWeightDisplayName,
                            onFontWeightSelected = { fontWeight ->
                                selectedFontWeight = fontWeight
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 搜索图标粗细设置行
                        IconWeightDropdownMenu(
                            title = stringResource(R.string.search_field_icon_weight),
                            selectedIconWeight = selectedIconWeight,
                            iconWeightDisplayName = iconWeightDisplayName,
                            onIconWeightSelected = { iconWeight ->
                                selectedIconWeight = iconWeight
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                    }
                }
                }
                // 标题栏设置分组标题
                // 设置标题
                item {
                Text(
                    text = "标题栏设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 标题栏设置
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 标题栏类型设置
                        TopAppBarTypeSetting(
                            selectedTopAppBarType = selectedTopAppBarType,
                            topAppBarTypeDisplayName = topAppBarTypeDisplayName,
                            onTopAppBarTypeSelected = { type ->
                                selectedTopAppBarType = type
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题栏高度设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showTopAppBarDialog = TopAppBarDialogType.HEIGHT
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "标题栏高度",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前标题栏高度值
                            Text(
                                text = "${currentSettings.topAppBarHeight}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题字体大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showTopAppBarDialog = TopAppBarDialogType.TITLE_FONT_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "标题字体大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前标题字体大小值
                            Text(
                                text = "${currentSettings.topAppBarTitleFontSize}sp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题字重设置
                        TitleFontWeightSetting(
                            selectedTitleFontWeight = selectedTitleFontWeight,
                            titleFontWeightDisplayName = titleFontWeightDisplayName,
                            onTitleFontWeightSelected = { fontWeight ->
                                selectedTitleFontWeight = fontWeight
                                saveSettingsImmediately()
                            },
                            uiSpacingConfig = uiSpacingConfig
                        )
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题垂直偏移设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showTopAppBarDialog = TopAppBarDialogType.TITLE_VERTICAL_OFFSET
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "标题垂直偏移",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前标题垂直偏移值
                            Text(
                                text = "${currentSettings.topAppBarTitleVerticalOffset}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 标题水平偏移设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showTopAppBarDialog = TopAppBarDialogType.TITLE_HORIZONTAL_OFFSET
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "标题水平偏移",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前标题水平偏移值
                            Text(
                                text = "${currentSettings.topAppBarTitleHorizontalOffset}dp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 界面标题字体大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showTopAppBarDialog = TopAppBarDialogType.SCREEN_TITLE_FONT_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "界面标题字体大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前界面标题字体大小值
                            Text(
                                text = "${currentSettings.screenTitleFontSize}sp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        // 分割线 - 使用主题感知的分割线组件
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                        // 表单标题字体大小设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showTopAppBarDialog = TopAppBarDialogType.FORM_SECTION_TITLE_FONT_SIZE
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "表单标题字体大小",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            // 显示当前表单标题字体大小值
                            Text(
                                text = "${currentSettings.formSectionTitleFontSize}sp",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                }
                // 用户体验设置分组标题
                // 设置标题
                item {
                Text(
                    text = "用户体验设置",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.padding(
                        start = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        end = uiSpacingConfig.settingsGroupTitleHorizontalPadding.dp,
                        top = uiSpacingConfig.settingsGroupTitleTopPadding.dp,
                        bottom = uiSpacingConfig.settingsGroupTitleBottomPadding.dp
                    )
                )
                }
                // 用户体验设置卡片
                // 设置卡片
                item {
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(0.dp)
                    ) {
                        // 体检结果延迟显示开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    checkupResultDelayEnabled = !checkupResultDelayEnabled
                                    saveSettingsImmediately()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "体检结果延迟显示",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = checkupResultDelayEnabled,
                                onCheckedChange = { enabled ->
                                    checkupResultDelayEnabled = enabled
                                    saveSettingsImmediately()
                                }
                            )
                        }

                        // 分割线
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 导航项显示控制设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showNavigationItemsVisibilityDialog = true
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "导航项显示控制",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            Icon(
                                imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                                contentDescription = "配置导航项显示",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(20.dp)
                            )
                        }

                        // 分割线
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 默认启动页面设置行 - 点击弹出对话框
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showDefaultStartupPageDialog = true
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "默认启动页面",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )

                            // 显示当前默认启动页面
                            val startupPageDisplayName = when (defaultStartupPage) {
                                "phone_checkup" -> "体检"
                                "quick_commands" -> "指令"
                                "command_templates" -> "探索"
                                "smart_reminders" -> "提醒"
                                "global_settings" -> "设置"
                                else -> "体检"
                            }

                            Text(
                                text = startupPageDisplayName,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )

                            Icon(
                                imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                                contentDescription = "选择默认启动页面",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(20.dp)
                            )
                        }

                        // 结果显示延迟时长设置行 - 只有在开关开启时才显示
                        if (checkupResultDelayEnabled) {
                            // 分割线
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        showCheckupDisplayDurationDialog = true
                                    }
                                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "延迟显示时长",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )
                                // 显示当前延迟显示时长值
                                Text(
                                    text = "${checkupDisplayDurationSeconds}秒",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }

                        // 分割线
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )

                        // 固定体检分数开关
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    fixedCheckupScoreEnabled = !fixedCheckupScoreEnabled
                                    saveSettingsImmediately()
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "固定体检分数",
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )
                            SkyBlueSwitch(
                                checked = fixedCheckupScoreEnabled,
                                onCheckedChange = { enabled ->
                                    fixedCheckupScoreEnabled = enabled
                                    saveSettingsImmediately()
                                }
                            )
                        }

                        // 固定体检分数值设置行 - 只有在开关开启时才显示
                        if (fixedCheckupScoreEnabled) {
                            // 分割线
                            themeContext.componentFactory.createDivider()(
                                DividerConfig(
                                    modifier = Modifier.padding(horizontal = uiSpacingConfig.dividerHorizontalPadding.dp),
                                    color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                                )
                            )

                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        showFixedCheckupScoreDialog = true
                                    }
                                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "固定分数值",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )
                                Text(
                                    text = "${fixedCheckupScore}分",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
                }
                // 实验性功能卡片 - 只有在解锁状态时才显示
                // 条件设置
                item {
                if (currentSettings.experimentalFeaturesUnlocked) {
                    Spacer(modifier = Modifier.height(12.dp))
                    ThemedCard(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(0.dp)
                        ) {
                            // 实验性功能开关
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        experimentalFeaturesEnabled = !experimentalFeaturesEnabled
                                        saveSettingsImmediately()
                                    }
                                    .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text(
                                        text = stringResource(R.string.experimental_features_enabled),
                                        style = MaterialTheme.typography.bodyLarge,
                                        color = MaterialTheme.colorScheme.onSurface,
                                        fontWeight = androidx.compose.ui.text.font.FontWeight.Medium
                                    )
                                    Text(
                                        text = stringResource(R.string.experimental_features_description),
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        modifier = Modifier.padding(top = 4.dp)
                                    )
                                }
                                SkyBlueSwitch(
                                    checked = experimentalFeaturesEnabled,
                                    onCheckedChange = { enabled ->
                                        experimentalFeaturesEnabled = enabled
                                        saveSettingsImmediately()
                                    }
                                )
                            }
                        }
                    }
                }
                }
        }

            // 模糊强度设置对话框
            if (showBlurIntensityDialog) {
                BlurIntensityDialog(
                    currentIntensity = blurConfig.getIntensityPercentage(),
                    onDismiss = { showBlurIntensityDialog = false },
                    onConfirm = { newIntensity ->
                        val intensity = newIntensity / 100f
                        blurConfigManager.updateBlurIntensity(intensity)
                        showBlurIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 背景透明度设置对话框
            if (showBackgroundAlphaDialog) {
                BackgroundAlphaDialog(
                    currentAlpha = blurConfig.getBackgroundAlphaPercentage(),
                    onDismiss = { showBackgroundAlphaDialog = false },
                    onConfirm = { newAlpha ->
                        val alpha = newAlpha / 100f
                        blurConfigManager.updateBackgroundAlpha(alpha)
                        showBackgroundAlphaDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 顶部栏模糊强度设置对话框
            if (showTopBarBlurIntensityDialog) {
                ComponentBlurIntensityDialog(
                    component = BlurComponent.TOP_BAR,
                    componentName = "顶部栏",
                    currentIntensity = blurConfig.getComponentIntensityPercentage(BlurComponent.TOP_BAR),
                    onDismiss = { showTopBarBlurIntensityDialog = false },
                    onConfirm = { newIntensity ->
                        val intensity = newIntensity / 100f
                        blurConfigManager.updateComponentBlurIntensity(BlurComponent.TOP_BAR, intensity)
                        showTopBarBlurIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 顶部栏背景透明度设置对话框
            if (showTopBarBackgroundAlphaDialog) {
                ComponentBackgroundAlphaDialog(
                    component = BlurComponent.TOP_BAR,
                    componentName = "顶部栏",
                    currentAlpha = blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.TOP_BAR),
                    onDismiss = { showTopBarBackgroundAlphaDialog = false },
                    onConfirm = { newAlpha ->
                        val alpha = newAlpha / 100f
                        blurConfigManager.updateComponentBackgroundAlpha(BlurComponent.TOP_BAR, alpha)
                        showTopBarBackgroundAlphaDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 底部栏模糊强度设置对话框
            if (showBottomBarBlurIntensityDialog) {
                ComponentBlurIntensityDialog(
                    component = BlurComponent.BOTTOM_BAR,
                    componentName = "底部栏",
                    currentIntensity = blurConfig.getComponentIntensityPercentage(BlurComponent.BOTTOM_BAR),
                    onDismiss = { showBottomBarBlurIntensityDialog = false },
                    onConfirm = { newIntensity ->
                        val intensity = newIntensity / 100f
                        blurConfigManager.updateComponentBlurIntensity(BlurComponent.BOTTOM_BAR, intensity)
                        showBottomBarBlurIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 底部栏背景透明度设置对话框
            if (showBottomBarBackgroundAlphaDialog) {
                ComponentBackgroundAlphaDialog(
                    component = BlurComponent.BOTTOM_BAR,
                    componentName = "底部栏",
                    currentAlpha = blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.BOTTOM_BAR),
                    onDismiss = { showBottomBarBackgroundAlphaDialog = false },
                    onConfirm = { newAlpha ->
                        val alpha = newAlpha / 100f
                        blurConfigManager.updateComponentBackgroundAlpha(BlurComponent.BOTTOM_BAR, alpha)
                        showBottomBarBackgroundAlphaDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 对话框模糊强度设置对话框
            if (showDialogBlurIntensityDialog) {
                ComponentBlurIntensityDialog(
                    component = BlurComponent.DIALOG,
                    componentName = "对话框",
                    currentIntensity = blurConfig.getComponentIntensityPercentage(BlurComponent.DIALOG),
                    onDismiss = { showDialogBlurIntensityDialog = false },
                    onConfirm = { newIntensity ->
                        val intensity = newIntensity / 100f
                        blurConfigManager.updateComponentBlurIntensity(BlurComponent.DIALOG, intensity)
                        showDialogBlurIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 对话框背景透明度设置对话框
            if (showDialogBackgroundAlphaDialog) {
                ComponentBackgroundAlphaDialog(
                    component = BlurComponent.DIALOG,
                    componentName = "对话框",
                    currentAlpha = blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.DIALOG),
                    onDismiss = { showDialogBackgroundAlphaDialog = false },
                    onConfirm = { newAlpha ->
                        val alpha = newAlpha / 100f
                        blurConfigManager.updateComponentBackgroundAlpha(BlurComponent.DIALOG, alpha)
                        showDialogBackgroundAlphaDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 覆盖层模糊强度设置对话框
            if (showOverlayBlurIntensityDialog) {
                ComponentBlurIntensityDialog(
                    component = BlurComponent.OVERLAY,
                    componentName = "覆盖层",
                    currentIntensity = blurConfig.getComponentIntensityPercentage(BlurComponent.OVERLAY),
                    onDismiss = { showOverlayBlurIntensityDialog = false },
                    onConfirm = { newIntensity ->
                        val intensity = newIntensity / 100f
                        blurConfigManager.updateComponentBlurIntensity(BlurComponent.OVERLAY, intensity)
                        showOverlayBlurIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 覆盖层背景透明度设置对话框
            if (showOverlayBackgroundAlphaDialog) {
                ComponentBackgroundAlphaDialog(
                    component = BlurComponent.OVERLAY,
                    componentName = "覆盖层",
                    currentAlpha = blurConfig.getComponentBackgroundAlphaPercentage(BlurComponent.OVERLAY),
                    onDismiss = { showOverlayBackgroundAlphaDialog = false },
                    onConfirm = { newAlpha ->
                        val alpha = newAlpha / 100f
                        blurConfigManager.updateComponentBackgroundAlpha(BlurComponent.OVERLAY, alpha)
                        showOverlayBackgroundAlphaDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 顶部栏模糊半径设置对话框
            if (showTopBarBlurRadiusDialog) {
                ComponentBlurRadiusDialog(
                    component = BlurComponent.TOP_BAR,
                    componentName = "顶部栏",
                    currentRadius = blurConfig.getComponentBlurRadiusValue(BlurComponent.TOP_BAR),
                    onDismiss = { showTopBarBlurRadiusDialog = false },
                    onConfirm = { newRadius ->
                        blurConfigManager.updateComponentBlurRadius(BlurComponent.TOP_BAR, newRadius.dp)
                        showTopBarBlurRadiusDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 底部栏模糊半径设置对话框
            if (showBottomBarBlurRadiusDialog) {
                ComponentBlurRadiusDialog(
                    component = BlurComponent.BOTTOM_BAR,
                    componentName = "底部栏",
                    currentRadius = blurConfig.getComponentBlurRadiusValue(BlurComponent.BOTTOM_BAR),
                    onDismiss = { showBottomBarBlurRadiusDialog = false },
                    onConfirm = { newRadius ->
                        blurConfigManager.updateComponentBlurRadius(BlurComponent.BOTTOM_BAR, newRadius.dp)
                        showBottomBarBlurRadiusDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 对话框模糊半径设置对话框
            if (showDialogBlurRadiusDialog) {
                ComponentBlurRadiusDialog(
                    component = BlurComponent.DIALOG,
                    componentName = "对话框",
                    currentRadius = blurConfig.getComponentBlurRadiusValue(BlurComponent.DIALOG),
                    onDismiss = { showDialogBlurRadiusDialog = false },
                    onConfirm = { newRadius ->
                        blurConfigManager.updateComponentBlurRadius(BlurComponent.DIALOG, newRadius.dp)
                        showDialogBlurRadiusDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 覆盖层模糊半径设置对话框
            if (showOverlayBlurRadiusDialog) {
                ComponentBlurRadiusDialog(
                    component = BlurComponent.OVERLAY,
                    componentName = "覆盖层",
                    currentRadius = blurConfig.getComponentBlurRadiusValue(BlurComponent.OVERLAY),
                    onDismiss = { showOverlayBlurRadiusDialog = false },
                    onConfirm = { newRadius ->
                        blurConfigManager.updateComponentBlurRadius(BlurComponent.OVERLAY, newRadius.dp)
                        showOverlayBlurRadiusDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 顶部栏噪声因子设置对话框
            if (showTopBarNoiseFactorDialog) {
                ComponentNoiseFactorDialog(
                    component = BlurComponent.TOP_BAR,
                    componentName = "顶部栏",
                    currentNoiseFactor = (blurConfig.getComponentNoiseFactor(BlurComponent.TOP_BAR) * 100).toInt(),
                    onDismiss = { showTopBarNoiseFactorDialog = false },
                    onConfirm = { newNoiseFactor ->
                        val noiseFactor = newNoiseFactor / 100f
                        blurConfigManager.updateComponentNoiseFactor(BlurComponent.TOP_BAR, noiseFactor)
                        showTopBarNoiseFactorDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 底部栏噪声因子设置对话框
            if (showBottomBarNoiseFactorDialog) {
                ComponentNoiseFactorDialog(
                    component = BlurComponent.BOTTOM_BAR,
                    componentName = "底部栏",
                    currentNoiseFactor = (blurConfig.getComponentNoiseFactor(BlurComponent.BOTTOM_BAR) * 100).toInt(),
                    onDismiss = { showBottomBarNoiseFactorDialog = false },
                    onConfirm = { newNoiseFactor ->
                        val noiseFactor = newNoiseFactor / 100f
                        blurConfigManager.updateComponentNoiseFactor(BlurComponent.BOTTOM_BAR, noiseFactor)
                        showBottomBarNoiseFactorDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 对话框噪声因子设置对话框
            if (showDialogNoiseFactorDialog) {
                ComponentNoiseFactorDialog(
                    component = BlurComponent.DIALOG,
                    componentName = "对话框",
                    currentNoiseFactor = (blurConfig.getComponentNoiseFactor(BlurComponent.DIALOG) * 100).toInt(),
                    onDismiss = { showDialogNoiseFactorDialog = false },
                    onConfirm = { newNoiseFactor ->
                        val noiseFactor = newNoiseFactor / 100f
                        blurConfigManager.updateComponentNoiseFactor(BlurComponent.DIALOG, noiseFactor)
                        showDialogNoiseFactorDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 覆盖层噪声因子设置对话框
            if (showOverlayNoiseFactorDialog) {
                ComponentNoiseFactorDialog(
                    component = BlurComponent.OVERLAY,
                    componentName = "覆盖层",
                    currentNoiseFactor = (blurConfig.getComponentNoiseFactor(BlurComponent.OVERLAY) * 100).toInt(),
                    onDismiss = { showOverlayNoiseFactorDialog = false },
                    onConfirm = { newNoiseFactor ->
                        val noiseFactor = newNoiseFactor / 100f
                        blurConfigManager.updateComponentNoiseFactor(BlurComponent.OVERLAY, noiseFactor)
                        showOverlayNoiseFactorDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 顶部栏色调强度设置对话框
            if (showTopBarTintIntensityDialog) {
                ComponentTintIntensityDialog(
                    component = BlurComponent.TOP_BAR,
                    componentName = "顶部栏",
                    currentTintIntensity = (blurConfig.getComponentTintIntensity(BlurComponent.TOP_BAR) * 10).toInt(),
                    onDismiss = { showTopBarTintIntensityDialog = false },
                    onConfirm = { newTintIntensity ->
                        val tintIntensity = newTintIntensity / 10f
                        blurConfigManager.updateComponentTintIntensity(BlurComponent.TOP_BAR, tintIntensity)
                        showTopBarTintIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 底部栏色调强度设置对话框
            if (showBottomBarTintIntensityDialog) {
                ComponentTintIntensityDialog(
                    component = BlurComponent.BOTTOM_BAR,
                    componentName = "底部栏",
                    currentTintIntensity = (blurConfig.getComponentTintIntensity(BlurComponent.BOTTOM_BAR) * 10).toInt(),
                    onDismiss = { showBottomBarTintIntensityDialog = false },
                    onConfirm = { newTintIntensity ->
                        val tintIntensity = newTintIntensity / 10f
                        blurConfigManager.updateComponentTintIntensity(BlurComponent.BOTTOM_BAR, tintIntensity)
                        showBottomBarTintIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 对话框色调强度设置对话框
            if (showDialogTintIntensityDialog) {
                ComponentTintIntensityDialog(
                    component = BlurComponent.DIALOG,
                    componentName = "对话框",
                    currentTintIntensity = (blurConfig.getComponentTintIntensity(BlurComponent.DIALOG) * 10).toInt(),
                    onDismiss = { showDialogTintIntensityDialog = false },
                    onConfirm = { newTintIntensity ->
                        val tintIntensity = newTintIntensity / 10f
                        blurConfigManager.updateComponentTintIntensity(BlurComponent.DIALOG, tintIntensity)
                        showDialogTintIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 覆盖层色调强度设置对话框
            if (showOverlayTintIntensityDialog) {
                ComponentTintIntensityDialog(
                    component = BlurComponent.OVERLAY,
                    componentName = "覆盖层",
                    currentTintIntensity = (blurConfig.getComponentTintIntensity(BlurComponent.OVERLAY) * 10).toInt(),
                    onDismiss = { showOverlayTintIntensityDialog = false },
                    onConfirm = { newTintIntensity ->
                        val tintIntensity = newTintIntensity / 10f
                        blurConfigManager.updateComponentTintIntensity(BlurComponent.OVERLAY, tintIntensity)
                        showOverlayTintIntensityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 卡片样式设置对话框
            showCardStyleDialog?.let { dialogType ->
                CardStyleSettingDialog(
                    dialogType = dialogType,
                    currentSettings = currentSettings,
                    cardStyleConfigManager = cardStyleConfigManager,
                    onDismiss = { showCardStyleDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // 主题颜色设置对话框
            showThemeColorDialog?.let { dialogType ->
                ThemeColorSettingDialog(
                    dialogType = dialogType,
                    currentSettings = currentSettings,
                    colorConfigManager = colorConfigManager,
                    onDismiss = { showThemeColorDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // 底部导航栏样式设置对话框
            showBottomNavStyleDialog?.let { dialogType ->
                BottomNavStyleSettingDialog(
                    dialogType = dialogType,
                    currentSettings = currentSettings,
                    bottomNavStyleConfigManager = bottomNavStyleConfigManager,
                    onDismiss = { showBottomNavStyleDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // 标题栏设置对话框
            showTopAppBarDialog?.let { dialogType ->
                TopAppBarSettingDialog(
                    dialogType = dialogType,
                    currentSettings = currentSettings,
                    onDismiss = { showTopAppBarDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // 标题栏按钮配置对话框
            showTopAppBarButtonDialog?.let { dialogType ->
                TopAppBarButtonSettingDialog(
                    dialogType = dialogType,
                    currentSettings = currentSettings,
                    onDismiss = { showTopAppBarButtonDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // 页面布局设置对话框
            showPageLayoutDialog?.let { dialogType ->
                PageLayoutSettingDialog(
                    dialogType = dialogType,
                    currentSettings = currentSettings,
                    pageLayoutConfigManager = pageLayoutConfigManager,
                    onDismiss = { showPageLayoutDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // UI间距设置对话框
            showUISpacingDialog?.let { dialogType ->
                UISpacingSettingDialog(
                    dialogType = dialogType,
                    uiSpacingConfigManager = uiSpacingConfigManager,
                    onDismiss = { showUISpacingDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // 对话框间距设置对话框
            showDialogSpacingDialog?.let { dialogType ->
                DialogSpacingSettingDialog(
                    dialogType = dialogType,
                    currentSettings = currentSettings,
                    dialogSpacingConfigManager = dialogSpacingConfigManager,
                    onDismiss = { showDialogSpacingDialog = null },
                    settingsRepository = settingsRepository
                )
            }

            // 小组件更新间隔设置对话框
            if (showWidgetUpdateIntervalDialog) {
                WidgetUpdateIntervalDialog(
                    currentInterval = widgetUpdateInterval.toIntOrNull() ?: 24,
                    onDismiss = { showWidgetUpdateIntervalDialog = false },
                    onConfirm = { newInterval ->
                        widgetUpdateInterval = newInterval.toString()

                        saveSettingsImmediately()
                        showWidgetUpdateIntervalDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 结果显示延迟时长设置对话框
            if (showCheckupDisplayDurationDialog) {
                CheckupDisplayDurationDialog(
                    currentDuration = checkupDisplayDurationSeconds.toIntOrNull() ?: 5,
                    onDismiss = { showCheckupDisplayDurationDialog = false },
                    onConfirm = { newDuration ->
                        checkupDisplayDurationSeconds = newDuration.toString()
                        saveSettingsImmediately()
                        showCheckupDisplayDurationDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 固定体检分数设置对话框
            if (showFixedCheckupScoreDialog) {
                FixedCheckupScoreDialog(
                    currentScore = fixedCheckupScore.toIntOrNull() ?: 100,
                    onDismiss = { showFixedCheckupScoreDialog = false },
                    onConfirm = { newScore ->
                        fixedCheckupScore = newScore.toString()
                        saveSettingsImmediately()
                        showFixedCheckupScoreDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 水波动效阈值设置对话框
            if (showWaterWaveThresholdDialog) {
                WaterWaveThresholdDialog(
                    currentThreshold = currentSettings.waterWaveStopThreshold,
                    onDismiss = { showWaterWaveThresholdDialog = false },
                    onConfirm = { newThreshold ->
                        val newSettings = currentSettings.copy(waterWaveStopThreshold = newThreshold)
                        settingsRepository.saveGlobalSettings(newSettings)
                        showWaterWaveThresholdDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 导航项显示控制对话框
            if (showNavigationItemsVisibilityDialog) {
                NavigationItemsVisibilityDialog(
                    currentVisibility = navigationItemsVisibility,
                    onDismiss = { showNavigationItemsVisibilityDialog = false },
                    onConfirm = { newVisibility ->
                        navigationItemsVisibility = newVisibility
                        saveSettingsImmediately()
                        showNavigationItemsVisibilityDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 默认启动页面设置对话框
            if (showDefaultStartupPageDialog) {
                DefaultStartupPageDialog(
                    currentStartupPage = defaultStartupPage,
                    onDismiss = { showDefaultStartupPageDialog = false },
                    onConfirm = { newStartupPage ->
                        defaultStartupPage = newStartupPage
                        saveSettingsImmediately()
                        showDefaultStartupPageDialog = false
                    },
                    settingsRepository = settingsRepository
                )
            }

            // 实验性功能激活区域 - 左下角透明点击区域
            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(32.dp)
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null
                    ) {
                        // 处理左下角白色背景区域点击
                        experimentalFeatureDetector?.handleClick(ExperimentalFeatureDetector.ClickTarget.BACKGROUND_AREA)
                    }
            )
        }
    }



/**
 * 小组件更新间隔设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与模糊强度设置对话框保持完全一致的布局和样式
 */
@Composable
private fun WidgetUpdateIntervalDialog(
    currentInterval: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentInterval) { mutableStateOf(currentInterval.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("更新间隔设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 更新间隔输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入更新间隔（小时）",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 提示文本
                Text(
                    text = "建议设置为6-24小时，过于频繁会影响电池续航",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val interval = inputText.toIntOrNull()
                    if (interval != null && interval >= 1) {
                        onConfirm(interval)
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}



/**
 * 检查是否有使用情况访问权限
 */
private fun hasUsageStatsPermission(context: Context): Boolean {
    val appOps = context.getSystemService(Context.APP_OPS_SERVICE) as android.app.AppOpsManager
    val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        appOps.unsafeCheckOpNoThrow(
            android.app.AppOpsManager.OPSTR_GET_USAGE_STATS,
            android.os.Process.myUid(),
            context.packageName
        )
    } else {
        appOps.checkOpNoThrow(
            android.app.AppOpsManager.OPSTR_GET_USAGE_STATS,
            android.os.Process.myUid(),
            context.packageName
        )
    }
    return mode == android.app.AppOpsManager.MODE_ALLOWED
}

/**
 * 打开使用情况访问权限设置页面
 */
private fun openUsageStatsSettings(context: Context) {
    val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
    context.startActivity(intent)
}

/**
 * 语言下拉菜单组件
 *
 * 性能优化特性：
 * 1. 局部状态管理，只有下拉菜单会重组
 * 2. 条件渲染，下拉菜单只在展开时渲染
 * 3. 使用remember减少不必要的重组
 * 4. 紧凑的UI设计，与其他设置项保持一致
 */
@Composable
private fun LanguageDropdownMenu(
    title: String,
    selectedLanguage: String,
    languageDisplayName: String,
    onLanguageSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    // 下拉菜单展开状态 - 局部状态，不影响其他组件
    var expanded by remember { mutableStateOf(false) }

    // 上下文 - 用于获取本地化字符串
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    Box {
        // 使用与外观主题下拉菜单相同的布局样式 - 整个区域都可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前语言名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = languageDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp) // 向右偏移与箭头协调
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                )
            }
        }

        // 条件渲染：只在展开时渲染下拉菜单，减少性能开销
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp), // 向右偏移到卡片右侧
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                val languageOptions = listOf(
                    LanguageManager.LANGUAGE_SYSTEM to context.withAppLanguage().getString(R.string.language_system_default),
                    LanguageManager.LANGUAGE_CHINESE to context.withAppLanguage().getString(R.string.language_chinese),
                    LanguageManager.LANGUAGE_ENGLISH to context.withAppLanguage().getString(R.string.language_english)
                )

                languageOptions.forEachIndexed { index, (value, displayName) ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中语言显示勾选图标
                                if (value == selectedLanguage) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onLanguageSelected(value)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 在语言选项之间添加分割线（除了最后一个）
                    if (index < languageOptions.size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 主题下拉菜单组件
 *
 * 采用与其他下拉菜单一致的统一架构
 */
@Composable
private fun ThemeDropdownMenu(
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentTheme by themeManager.currentTheme

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    // 下拉菜单展开状态
    var expanded by remember { mutableStateOf(false) }

    Box {
        // 使用与其他下拉菜单相同的布局样式 - 整个区域都可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = "外观主题",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前主题名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = currentTheme.displayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp) // 向右偏移与箭头协调
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                )
            }
        }

        // 条件渲染：只在展开时渲染下拉菜单
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp), // 向右偏移到卡片右侧
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                AppTheme.values().forEachIndexed { index, theme ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = theme.displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中主题显示勾选图标
                                if (theme == currentTheme) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            themeManager.setTheme(theme)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 在主题选项之间添加分割线（除了最后一个）
                    if (index < AppTheme.values().size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 字重选择下拉菜单组件
 *
 * 专用于天空蓝主题的搜索框提示文字字重设置
 */
@Composable
private fun FontWeightDropdownMenu(
    title: String,
    selectedFontWeight: String,
    fontWeightDisplayName: String,
    onFontWeightSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    var expanded by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    Box {
        // 使用与外观主题下拉菜单相同的布局样式 - 整个区域都可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前字重名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = fontWeightDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp) // 向右偏移与箭头协调
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                )
            }
        }

        // 条件渲染：只在展开时渲染下拉菜单
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp), // 向右偏移到卡片右侧
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                val fontWeightOptions = listOf(
                    "normal" to context.withAppLanguage().getString(R.string.font_weight_regular),
                    "medium" to context.withAppLanguage().getString(R.string.font_weight_medium),
                    "bold" to context.withAppLanguage().getString(R.string.font_weight_bold)
                )

                fontWeightOptions.forEachIndexed { index, (value, displayName) ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中字重显示勾选图标
                                if (value == selectedFontWeight) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onFontWeightSelected(value)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 在字重选项之间添加分割线（除了最后一个）
                    if (index < fontWeightOptions.size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 图标粗细选择下拉菜单组件
 *
 * 专用于天空蓝主题的搜索框图标粗细设置
 */
@Composable
private fun IconWeightDropdownMenu(
    title: String,
    selectedIconWeight: String,
    iconWeightDisplayName: String,
    onIconWeightSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    var expanded by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    Box {
        // 使用与外观主题下拉菜单相同的布局样式 - 整个区域都可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前图标粗细名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = iconWeightDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp) // 向右偏移与箭头协调
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                )
            }
        }

        // 条件渲染：只在展开时渲染下拉菜单
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp), // 向右偏移到卡片右侧
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                val iconWeightOptions = listOf(
                    "regular" to context.withAppLanguage().getString(R.string.icon_weight_regular),
                    "medium" to context.withAppLanguage().getString(R.string.icon_weight_medium),
                    "bold" to context.withAppLanguage().getString(R.string.icon_weight_bold)
                )

                iconWeightOptions.forEachIndexed { index, (value, displayName) ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中图标粗细显示勾选图标
                                if (value == selectedIconWeight) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onIconWeightSelected(value)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 在图标粗细选项之间添加分割线（除了最后一个）
                    if (index < iconWeightOptions.size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 标题字重设置组件
 */
@Composable
private fun TitleFontWeightSetting(
    selectedTitleFontWeight: String,
    titleFontWeightDisplayName: String,
    onTitleFontWeightSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    var expanded by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    Box {
        // 使用与外观主题下拉菜单相同的布局样式 - 整个区域都可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = "标题字重",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前字重名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = titleFontWeightDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp) // 向右偏移与箭头协调
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                )
            }
        }

        DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp), // 向右偏移到卡片右侧
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                val titleFontWeightOptions = listOf(
                    "normal" to "常规",
                    "medium" to "中等",
                    "bold" to "粗体"
                )

                titleFontWeightOptions.forEachIndexed { index, (value, displayName) ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中标题字重显示勾选图标
                                if (value == selectedTitleFontWeight) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onTitleFontWeightSelected(value)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 在标题字重选项之间添加分割线（除了最后一个）
                    if (index < titleFontWeightOptions.size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
    }
}

/**
 * 卡片样式设置项组件
 *
 * 用于显示和编辑单个卡片样式配置项
 */
@Composable
private fun CardStyleSettingItem(
    title: String,
    description: String,
    currentValue: Int,
    range: IntRange,
    unit: String,
    onValueChange: (Int) -> Unit
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )

        Text(
            text = description,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 4.dp)
        )

        Spacer(modifier = Modifier.height(12.dp))

        // 数字输入框 - 使用LaunchedEffect确保状态同步
        var valueText by remember { mutableStateOf(currentValue.toString()) }
        var isError by remember { mutableStateOf(false) }

        // 当currentValue变化时，更新valueText
        LaunchedEffect(currentValue) {
            valueText = currentValue.toString()
            isError = false
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            OutlinedTextField(
                value = valueText,
                onValueChange = { newValue ->
                    valueText = newValue
                    val value = newValue.toIntOrNull()
                    if (value != null && value in range) {
                        isError = false
                        onValueChange(value)
                    } else {
                        isError = true
                    }
                },
                label = { Text("$title ($unit)") },
                suffix = { Text(unit) },
                isError = isError,
                supportingText = if (isError) {
                    { Text("请输入${range.first}-${range.last}之间的数值") }
                } else null,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.width(140.dp)
            )

            // 预设按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                val presets = when {
                    range.last <= 16 -> listOf(range.first, (range.first + range.last) / 2, range.last)
                    range.last <= 32 -> listOf(range.first, (range.first + range.last) / 2, range.last)
                    else -> listOf(range.first, (range.first + range.last) / 2, range.last)
                }

                presets.forEach { preset ->
                    OutlinedButton(
                        onClick = {
                            valueText = preset.toString()
                            isError = false
                            onValueChange(preset)
                        },
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text(preset.toString(), style = MaterialTheme.typography.bodySmall)
                    }
                }
            }
        }
    }
}

/**
 * 底部导航栏排列方式下拉菜单组件
 */
@Composable
private fun BottomNavArrangementDropdownMenu(
    selectedArrangement: String,
    arrangementDisplayName: String,
    onArrangementSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    var expanded by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    Box {
        // 使用与外观主题下拉菜单相同的布局样式 - 整个区域都可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = "导航项排列方式",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前排列方式名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = arrangementDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp) // 向右偏移与箭头协调
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                )
            }
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            offset = DpOffset(x = 122.dp, y = 11.dp), // 向右偏移到卡片右侧
            modifier = Modifier
                .widthIn(min = 200.dp)
                .background(
                    color = MaterialTheme.colorScheme.surfaceContainerLow,
                    shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                ),
            shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
        ) {
            val arrangements = listOf(
                "spaceEvenly" to "均匀分布",
                "spaceBetween" to "两端对齐",
                "spaceAround" to "环绕分布"
            )

            arrangements.forEachIndexed { index, (value, displayName) ->
                DropdownMenuItem(
                    text = {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = displayName,
                                color = MaterialTheme.colorScheme.onSurface,
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )

                            // 当前选中排列方式显示勾选图标
                            if (value == selectedArrangement) {
                                Icon(
                                    imageVector = Icons.Default.Check,
                                    contentDescription = "已选中",
                                    tint = MaterialTheme.colorScheme.onSurface,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                    },
                    onClick = {
                        onArrangementSelected(value)
                        expanded = false
                    },
                    colors = MenuDefaults.itemColors(
                        textColor = MaterialTheme.colorScheme.onSurface
                    ),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                )

                // 在排列方式选项之间添加分割线（除了最后一个）
                if (index < arrangements.size - 1) {
                    themeContext.componentFactory.createDivider()(
                        DividerConfig(
                            modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                            color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                        )
                    )
                }
            }
        }
    }
}

/**
 * 模糊强度设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 */
@Composable
private fun BlurIntensityDialog(
    currentIntensity: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentIntensity) { mutableStateOf(currentIntensity.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("模糊强度设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 模糊强度输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入模糊强度",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致
                Text(
                    text = "范围：10-100（%）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val intensity = inputText.toIntOrNull()
                    if (intensity != null && intensity in 10..100) {
                        onConfirm(intensity)
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 卡片样式设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与模糊强度设置对话框保持完全一致的布局和样式
 */
@Composable
private fun CardStyleSettingDialog(
    dialogType: CardStyleDialogType,
    currentSettings: GlobalSettings,
    cardStyleConfigManager: CardStyleConfigurationManager,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    // 获取当前值
    val currentValue = when (dialogType) {
        CardStyleDialogType.CORNER_RADIUS -> currentSettings.cardCornerRadius
        CardStyleDialogType.HORIZONTAL_PADDING -> currentSettings.cardDefaultHorizontalPadding
        CardStyleDialogType.VERTICAL_PADDING -> currentSettings.cardDefaultVerticalPadding
        CardStyleDialogType.SETTINGS_VERTICAL_PADDING -> currentSettings.cardSettingsVerticalPadding
        CardStyleDialogType.ITEM_SPACING -> currentSettings.cardItemSpacing
        CardStyleDialogType.SECTION_SPACING -> currentSettings.cardSectionSpacing
        CardStyleDialogType.CONTENT_VERTICAL_SPACING -> currentSettings.cardContentVerticalSpacing
        CardStyleDialogType.CONTENT_HORIZONTAL_SPACING -> currentSettings.cardContentHorizontalSpacing
        CardStyleDialogType.SELECTED_BORDER_WIDTH -> currentSettings.cardSelectedBorderWidth
        CardStyleDialogType.CARD_TITLE_FONT_SIZE -> currentSettings.cardTitleFontSize
        CardStyleDialogType.CARD_CONTENT_FONT_SIZE -> currentSettings.cardContentFontSize
        CardStyleDialogType.CARD_ICON_SIZE -> currentSettings.cardIconSize
    }

    var inputText by remember(currentValue) { mutableStateOf(currentValue.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入${dialogType.title}",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = "范围：${dialogType.range.first}-${dialogType.range.last}（${dialogType.unit}）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val value = inputText.toIntOrNull()
                    if (value != null && value in dialogType.range) {
                        // 根据对话框类型调用相应的更新方法
                        when (dialogType) {
                            CardStyleDialogType.CORNER_RADIUS -> cardStyleConfigManager.updateCornerRadius(value)
                            CardStyleDialogType.HORIZONTAL_PADDING -> cardStyleConfigManager.updateDefaultHorizontalPadding(value)
                            CardStyleDialogType.VERTICAL_PADDING -> cardStyleConfigManager.updateDefaultVerticalPadding(value)
                            CardStyleDialogType.SETTINGS_VERTICAL_PADDING -> cardStyleConfigManager.updateSettingsVerticalPadding(value)
                            CardStyleDialogType.ITEM_SPACING -> cardStyleConfigManager.updateItemSpacing(value)
                            CardStyleDialogType.SECTION_SPACING -> cardStyleConfigManager.updateSectionSpacing(value)
                            CardStyleDialogType.CONTENT_VERTICAL_SPACING -> cardStyleConfigManager.updateContentVerticalSpacing(value)
                            CardStyleDialogType.CONTENT_HORIZONTAL_SPACING -> cardStyleConfigManager.updateContentHorizontalSpacing(value)
                            CardStyleDialogType.SELECTED_BORDER_WIDTH -> cardStyleConfigManager.updateSelectedBorderWidth(value)
                            CardStyleDialogType.CARD_TITLE_FONT_SIZE -> cardStyleConfigManager.updateTitleFontSize(value)
                            CardStyleDialogType.CARD_CONTENT_FONT_SIZE -> cardStyleConfigManager.updateContentFontSize(value)
                            CardStyleDialogType.CARD_ICON_SIZE -> cardStyleConfigManager.updateIconSize(value)
                        }
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 页面布局设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与卡片样式设置对话框保持完全一致的布局和样式
 */
@Composable
private fun PageLayoutSettingDialog(
    dialogType: PageLayoutDialogType,
    currentSettings: GlobalSettings,
    pageLayoutConfigManager: PageLayoutConfigurationManager,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    // 获取当前值
    val currentValue = when (dialogType) {
        PageLayoutDialogType.CONTENT_HORIZONTAL_PADDING -> currentSettings.pageContentHorizontalPadding
        PageLayoutDialogType.SEARCH_FIELD_MARGIN -> currentSettings.pageSearchFieldMargin
        PageLayoutDialogType.BOTTOM_PADDING -> currentSettings.pageBottomPadding
        PageLayoutDialogType.HEADER_SPACING -> currentSettings.pageHeaderSpacing
        PageLayoutDialogType.SCROLL_CONTENT_SPACING -> currentSettings.pageScrollContentSpacing
    }

    var inputText by remember(currentValue) { mutableStateOf(currentValue.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入${dialogType.title}",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = "范围：${dialogType.range.first}-${dialogType.range.last}（${dialogType.unit}）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val value = inputText.toIntOrNull()
                    if (value != null && value in dialogType.range) {
                        // 根据对话框类型调用相应的更新方法
                        when (dialogType) {
                            PageLayoutDialogType.CONTENT_HORIZONTAL_PADDING -> pageLayoutConfigManager.updateContentHorizontalPadding(value)
                            PageLayoutDialogType.SEARCH_FIELD_MARGIN -> pageLayoutConfigManager.updateSearchFieldMargin(value)
                            PageLayoutDialogType.BOTTOM_PADDING -> pageLayoutConfigManager.updateBottomPadding(value)
                            PageLayoutDialogType.HEADER_SPACING -> pageLayoutConfigManager.updateHeaderSpacing(value)
                            PageLayoutDialogType.SCROLL_CONTENT_SPACING -> pageLayoutConfigManager.updateScrollContentSpacing(value)
                        }
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * UI间距设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与卡片样式设置对话框保持完全一致的布局和样式
 */
@Composable
private fun UISpacingSettingDialog(
    dialogType: UISpacingDialogType,
    uiSpacingConfigManager: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    // 获取当前值
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()
    val currentValue = when (dialogType) {
        UISpacingDialogType.SETTINGS_ITEM_VERTICAL_PADDING -> uiSpacingConfig.settingsItemVerticalPadding
        UISpacingDialogType.DIVIDER_HORIZONTAL_PADDING -> uiSpacingConfig.dividerHorizontalPadding
        UISpacingDialogType.SETTINGS_CARD_PADDING -> uiSpacingConfig.settingsCardPadding
        UISpacingDialogType.SETTINGS_ITEM_SPACING -> uiSpacingConfig.settingsItemSpacing
        UISpacingDialogType.SETTINGS_TITLE_SPACING -> uiSpacingConfig.settingsTitleSpacing
        UISpacingDialogType.SETTINGS_DESCRIPTION_SPACING -> uiSpacingConfig.settingsDescriptionSpacing
        UISpacingDialogType.SETTINGS_GROUP_TITLE_HORIZONTAL_PADDING -> uiSpacingConfig.settingsGroupTitleHorizontalPadding
        UISpacingDialogType.SETTINGS_GROUP_TITLE_TOP_PADDING -> uiSpacingConfig.settingsGroupTitleTopPadding
        UISpacingDialogType.SETTINGS_GROUP_TITLE_BOTTOM_PADDING -> uiSpacingConfig.settingsGroupTitleBottomPadding
        UISpacingDialogType.GLOBAL_SETTINGS_ITEM_SPACING -> uiSpacingConfig.globalSettingsItemSpacing
    }

    var inputText by remember(currentValue) { mutableStateOf(currentValue.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManagerLocal = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfigLocal = uiSpacingConfigManagerLocal.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfigLocal.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入${dialogType.title}",
                        modifier = Modifier.padding(bottom = uiSpacingConfigLocal.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = "范围：${dialogType.range.first}-${dialogType.range.last}（${dialogType.unit}）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val value = inputText.toIntOrNull()
                    if (value != null && value in dialogType.range) {
                        when (dialogType) {
                            UISpacingDialogType.SETTINGS_ITEM_VERTICAL_PADDING -> uiSpacingConfigManager.updateSettingsItemVerticalPadding(value)
                            UISpacingDialogType.DIVIDER_HORIZONTAL_PADDING -> uiSpacingConfigManager.updateDividerHorizontalPadding(value)
                            UISpacingDialogType.SETTINGS_CARD_PADDING -> uiSpacingConfigManager.updateSettingsCardPadding(value)
                            UISpacingDialogType.SETTINGS_ITEM_SPACING -> uiSpacingConfigManager.updateSettingsItemSpacing(value)
                            UISpacingDialogType.SETTINGS_TITLE_SPACING -> uiSpacingConfigManager.updateSettingsTitleSpacing(value)
                            UISpacingDialogType.SETTINGS_DESCRIPTION_SPACING -> uiSpacingConfigManager.updateSettingsDescriptionSpacing(value)
                            UISpacingDialogType.SETTINGS_GROUP_TITLE_HORIZONTAL_PADDING -> uiSpacingConfigManager.updateSettingsGroupTitleHorizontalPadding(value)
                            UISpacingDialogType.SETTINGS_GROUP_TITLE_TOP_PADDING -> uiSpacingConfigManager.updateSettingsGroupTitleTopPadding(value)
                            UISpacingDialogType.SETTINGS_GROUP_TITLE_BOTTOM_PADDING -> uiSpacingConfigManager.updateSettingsGroupTitleBottomPadding(value)
                            UISpacingDialogType.GLOBAL_SETTINGS_ITEM_SPACING -> uiSpacingConfigManager.updateGlobalSettingsItemSpacing(value)
                        }
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 对话框间距设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与卡片样式设置对话框保持完全一致的布局和样式
 */
@Composable
private fun DialogSpacingSettingDialog(
    dialogType: DialogSpacingDialogType,
    currentSettings: GlobalSettings,
    dialogSpacingConfigManager: DialogSpacingConfigurationManager,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    // 获取当前值
    val currentValue = when (dialogType) {
        DialogSpacingDialogType.OUTER_PADDING -> currentSettings.dialogOuterPadding
        DialogSpacingDialogType.ICON_BOTTOM_PADDING -> currentSettings.dialogIconBottomPadding
        DialogSpacingDialogType.TITLE_BOTTOM_PADDING -> currentSettings.dialogTitleBottomPadding
        DialogSpacingDialogType.CONTENT_BOTTOM_PADDING -> currentSettings.dialogContentBottomPadding
        DialogSpacingDialogType.BUTTON_TOP_PADDING -> currentSettings.dialogButtonTopPadding
        DialogSpacingDialogType.BUTTON_BOTTOM_PADDING -> currentSettings.dialogButtonBottomPadding
        DialogSpacingDialogType.TITLE_FONT_SIZE -> currentSettings.dialogTitleFontSize
        DialogSpacingDialogType.DIVIDER_HORIZONTAL_PADDING -> currentSettings.dialogDividerHorizontalPadding
    }

    var inputText by remember(currentValue) { mutableStateOf(currentValue.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入${dialogType.title}",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = "范围：${dialogType.range.first}-${dialogType.range.last}（${dialogType.unit}）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val value = inputText.toIntOrNull()
                    if (value != null && value in dialogType.range) {
                        // 根据对话框类型调用相应的更新方法
                        when (dialogType) {
                            DialogSpacingDialogType.OUTER_PADDING -> dialogSpacingConfigManager.updateOuterPadding(value)
                            DialogSpacingDialogType.ICON_BOTTOM_PADDING -> dialogSpacingConfigManager.updateIconBottomPadding(value)
                            DialogSpacingDialogType.TITLE_BOTTOM_PADDING -> dialogSpacingConfigManager.updateTitleBottomPadding(value)
                            DialogSpacingDialogType.CONTENT_BOTTOM_PADDING -> dialogSpacingConfigManager.updateContentBottomPadding(value)
                            DialogSpacingDialogType.BUTTON_TOP_PADDING -> dialogSpacingConfigManager.updateButtonTopPadding(value)
                            DialogSpacingDialogType.BUTTON_BOTTOM_PADDING -> dialogSpacingConfigManager.updateButtonBottomPadding(value)
                            DialogSpacingDialogType.TITLE_FONT_SIZE -> dialogSpacingConfigManager.updateTitleFontSize(value)
                            DialogSpacingDialogType.DIVIDER_HORIZONTAL_PADDING -> dialogSpacingConfigManager.updateDividerHorizontalPadding(value)
                        }
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 底部导航栏样式设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与卡片样式设置对话框保持完全一致的布局和样式
 */
@Composable
private fun BottomNavStyleSettingDialog(
    dialogType: BottomNavStyleDialogType,
    currentSettings: GlobalSettings,
    bottomNavStyleConfigManager: com.weinuo.quickcommands.ui.theme.manager.BottomNavigationStyleConfigurationManager,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    val context = LocalContext.current

    // 获取当前值
    val currentValue = when (dialogType) {
        BottomNavStyleDialogType.HEIGHT -> currentSettings.bottomNavHeight
        BottomNavStyleDialogType.HORIZONTAL_PADDING -> currentSettings.bottomNavHorizontalPadding
        BottomNavStyleDialogType.VERTICAL_PADDING -> currentSettings.bottomNavVerticalPadding
        BottomNavStyleDialogType.ITEM_CORNER_RADIUS -> currentSettings.bottomNavItemCornerRadius
        BottomNavStyleDialogType.ITEM_OUTER_PADDING -> currentSettings.bottomNavItemOuterPadding
        BottomNavStyleDialogType.ITEM_INNER_PADDING -> currentSettings.bottomNavItemVerticalPadding
        BottomNavStyleDialogType.ICON_SIZE -> currentSettings.bottomNavIconSize
        BottomNavStyleDialogType.ICON_TEXT_SPACING -> currentSettings.bottomNavIconTextSpacing
        BottomNavStyleDialogType.TEXT_FONT_SIZE -> currentSettings.bottomNavTextFontSize
    }

    var inputText by remember(currentValue) { mutableStateOf(currentValue.toString()) }

    // 获取UI间距配置
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入${dialogType.title}",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = "范围：${dialogType.range.first}-${dialogType.range.last}（${dialogType.unit}）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val value = inputText.toIntOrNull()
                    if (value != null && value in dialogType.range) {
                        // 根据对话框类型调用相应的更新方法
                        when (dialogType) {
                            BottomNavStyleDialogType.HEIGHT -> bottomNavStyleConfigManager.updateHeight(value)
                            BottomNavStyleDialogType.HORIZONTAL_PADDING -> bottomNavStyleConfigManager.updateHorizontalPadding(value)
                            BottomNavStyleDialogType.VERTICAL_PADDING -> bottomNavStyleConfigManager.updateVerticalPadding(value)
                            BottomNavStyleDialogType.ITEM_CORNER_RADIUS -> bottomNavStyleConfigManager.updateItemCornerRadius(value)
                            BottomNavStyleDialogType.ITEM_OUTER_PADDING -> bottomNavStyleConfigManager.updateItemOuterPadding(value)
                            BottomNavStyleDialogType.ITEM_INNER_PADDING -> bottomNavStyleConfigManager.updateItemVerticalPadding(value)
                            BottomNavStyleDialogType.ICON_SIZE -> bottomNavStyleConfigManager.updateIconSize(value)
                            BottomNavStyleDialogType.ICON_TEXT_SPACING -> bottomNavStyleConfigManager.updateIconTextSpacing(value)
                            BottomNavStyleDialogType.TEXT_FONT_SIZE -> bottomNavStyleConfigManager.updateTextFontSize(value)
                        }
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 主题颜色设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与卡片样式设置对话框保持完全一致的布局和样式
 */
@Composable
private fun ThemeColorSettingDialog(
    dialogType: ThemeColorDialogType,
    currentSettings: GlobalSettings,
    colorConfigManager: SkyBlueColorConfigurationManager,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    // 获取当前颜色值
    val currentColorString = when (dialogType) {
        ThemeColorDialogType.PRIMARY -> currentSettings.skyBluePrimary
        ThemeColorDialogType.ON_PRIMARY -> currentSettings.skyBlueOnPrimary
        ThemeColorDialogType.PRIMARY_CONTAINER -> currentSettings.skyBluePrimaryContainer
        ThemeColorDialogType.ON_PRIMARY_CONTAINER -> currentSettings.skyBlueOnPrimaryContainer
        ThemeColorDialogType.SECONDARY -> currentSettings.skyBlueSecondary
        ThemeColorDialogType.ON_SECONDARY -> currentSettings.skyBlueOnSecondary
        ThemeColorDialogType.SECONDARY_CONTAINER -> currentSettings.skyBlueSecondaryContainer
        ThemeColorDialogType.ON_SECONDARY_CONTAINER -> currentSettings.skyBlueOnSecondaryContainer
        ThemeColorDialogType.BACKGROUND -> currentSettings.skyBlueBackground
        ThemeColorDialogType.ON_BACKGROUND -> currentSettings.skyBlueOnBackground
        ThemeColorDialogType.SURFACE -> currentSettings.skyBlueSurface
        ThemeColorDialogType.ON_SURFACE -> currentSettings.skyBlueOnSurface
        ThemeColorDialogType.CONFIRM -> currentSettings.skyBlueConfirm
        ThemeColorDialogType.FONT_EMPHASIZE -> currentSettings.skyBlueFontEmphasize
        ThemeColorDialogType.ICON_EMPHASIZE -> currentSettings.skyBlueIconEmphasize
        ThemeColorDialogType.ICON_SUB_EMPHASIZE -> currentSettings.skyBlueIconSubEmphasize
        ThemeColorDialogType.BACKGROUND_EMPHASIZE -> currentSettings.skyBlueBackgroundEmphasize
        ThemeColorDialogType.BACKGROUND_FOCUS -> currentSettings.skyBlueBackgroundFocus
        ThemeColorDialogType.BOTTOM_NAV_BACKGROUND -> currentSettings.skyBlueBottomNavBackground
        ThemeColorDialogType.BOTTOM_NAV_SELECTED_ICON -> currentSettings.skyBlueBottomNavSelectedIcon
        ThemeColorDialogType.BOTTOM_NAV_UNSELECTED_ICON -> currentSettings.skyBlueBottomNavUnselectedIcon
        ThemeColorDialogType.TOP_BAR_BACKGROUND -> currentSettings.skyBlueTopBarBackground
    }

    var inputText by remember(currentColorString) { mutableStateOf(currentColorString) }
    var isError by remember { mutableStateOf(false) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    // 解析当前颜色用于预览
    val previewColor = remember(inputText) {
        if (SkyBlueColorConfigurationManager.isValidColorString(inputText)) {
            SkyBlueColorConfigurationManager.parseColorString(inputText)
        } else {
            SkyBlueColorConfigurationManager.parseColorString(currentColorString)
        }
    }

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // 颜色输入和预览
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 颜色输入框
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            inputText = newValue
                            val isValid = SkyBlueColorConfigurationManager.isValidColorString(newValue)
                            isError = !isValid
                        },
                        placeholder = "0xFF123456",
                        modifier = Modifier.weight(1f),
                        settingsRepository = settingsRepository
                    )

                    // 颜色预览
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                color = previewColor,
                                shape = RoundedCornerShape(8.dp)
                            )
                    )
                }

                // 支持格式说明 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = if (isError) {
                        "请输入有效的颜色代码（支持格式：0xFF123456、#123456、123456）"
                    } else {
                        "支持格式：0xFF123456、#123456、123456"
                    },
                    color = if (isError) Color.Red else Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    if (SkyBlueColorConfigurationManager.isValidColorString(inputText)) {
                        // 根据对话框类型更新相应的颜色配置
                        when (dialogType) {
                            ThemeColorDialogType.PRIMARY -> colorConfigManager.updatePrimary(inputText)
                            ThemeColorDialogType.ON_PRIMARY -> colorConfigManager.updateOnPrimary(inputText)
                            ThemeColorDialogType.PRIMARY_CONTAINER -> colorConfigManager.updatePrimaryContainer(inputText)
                            ThemeColorDialogType.ON_PRIMARY_CONTAINER -> colorConfigManager.updateOnPrimaryContainer(inputText)
                            ThemeColorDialogType.SECONDARY -> colorConfigManager.updateSecondary(inputText)
                            ThemeColorDialogType.ON_SECONDARY -> colorConfigManager.updateOnSecondary(inputText)
                            ThemeColorDialogType.SECONDARY_CONTAINER -> colorConfigManager.updateSecondaryContainer(inputText)
                            ThemeColorDialogType.ON_SECONDARY_CONTAINER -> colorConfigManager.updateOnSecondaryContainer(inputText)
                            ThemeColorDialogType.BACKGROUND -> colorConfigManager.updateBackground(inputText)
                            ThemeColorDialogType.ON_BACKGROUND -> colorConfigManager.updateOnBackground(inputText)
                            ThemeColorDialogType.SURFACE -> colorConfigManager.updateSurface(inputText)
                            ThemeColorDialogType.ON_SURFACE -> colorConfigManager.updateOnSurface(inputText)
                            ThemeColorDialogType.CONFIRM -> colorConfigManager.updateConfirm(inputText)
                            ThemeColorDialogType.FONT_EMPHASIZE -> colorConfigManager.updateFontEmphasize(inputText)
                            ThemeColorDialogType.ICON_EMPHASIZE -> colorConfigManager.updateIconEmphasize(inputText)
                            ThemeColorDialogType.ICON_SUB_EMPHASIZE -> colorConfigManager.updateIconSubEmphasize(inputText)
                            ThemeColorDialogType.BACKGROUND_EMPHASIZE -> colorConfigManager.updateBackgroundEmphasize(inputText)
                            ThemeColorDialogType.BACKGROUND_FOCUS -> colorConfigManager.updateBackgroundFocus(inputText)
                            ThemeColorDialogType.BOTTOM_NAV_BACKGROUND -> colorConfigManager.updateBottomNavBackground(inputText)
                            ThemeColorDialogType.BOTTOM_NAV_SELECTED_ICON -> colorConfigManager.updateBottomNavSelectedIcon(inputText)
                            ThemeColorDialogType.BOTTOM_NAV_UNSELECTED_ICON -> colorConfigManager.updateBottomNavUnselectedIcon(inputText)
                            ThemeColorDialogType.TOP_BAR_BACKGROUND -> colorConfigManager.updateTopBarBackground(inputText)
                        }
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 标题栏设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与底部导航栏样式设置对话框保持完全一致的布局和样式
 */
@Composable
private fun TopAppBarSettingDialog(
    dialogType: TopAppBarDialogType,
    currentSettings: GlobalSettings,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    val context = LocalContext.current

    // 获取当前值
    val currentValue = when (dialogType) {
        TopAppBarDialogType.HEIGHT -> currentSettings.topAppBarHeight
        TopAppBarDialogType.TITLE_FONT_SIZE -> currentSettings.topAppBarTitleFontSize
        TopAppBarDialogType.TITLE_VERTICAL_OFFSET -> currentSettings.topAppBarTitleVerticalOffset
        TopAppBarDialogType.TITLE_HORIZONTAL_OFFSET -> currentSettings.topAppBarTitleHorizontalOffset
        TopAppBarDialogType.SCREEN_TITLE_FONT_SIZE -> currentSettings.screenTitleFontSize
        TopAppBarDialogType.FORM_SECTION_TITLE_FONT_SIZE -> currentSettings.formSectionTitleFontSize
    }

    var inputText by remember(currentValue) { mutableStateOf(currentValue.toString()) }

    // 获取UI间距配置
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            // 支持负数输入（用于偏移设置）
                            val isOffsetType = dialogType == TopAppBarDialogType.TITLE_VERTICAL_OFFSET ||
                                             dialogType == TopAppBarDialogType.TITLE_HORIZONTAL_OFFSET
                            if (newValue.isEmpty() ||
                                (isOffsetType && (newValue == "-" || (newValue.all { it.isDigit() || it == '-' } && newValue.count { it == '-' } <= 1 && newValue.length <= 4))) ||
                                (!isOffsetType && newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入${dialogType.title}",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = "范围：${dialogType.range.first}-${dialogType.range.last}（${dialogType.unit}）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val value = inputText.toIntOrNull()
                    if (value != null && value in dialogType.range) {
                        // 根据对话框类型调用相应的更新方法
                        val newSettings = when (dialogType) {
                            TopAppBarDialogType.HEIGHT -> currentSettings.copy(topAppBarHeight = value)
                            TopAppBarDialogType.TITLE_FONT_SIZE -> currentSettings.copy(topAppBarTitleFontSize = value)
                            TopAppBarDialogType.TITLE_VERTICAL_OFFSET -> currentSettings.copy(topAppBarTitleVerticalOffset = value)
                            TopAppBarDialogType.TITLE_HORIZONTAL_OFFSET -> currentSettings.copy(topAppBarTitleHorizontalOffset = value)
                            TopAppBarDialogType.SCREEN_TITLE_FONT_SIZE -> currentSettings.copy(screenTitleFontSize = value)
                            TopAppBarDialogType.FORM_SECTION_TITLE_FONT_SIZE -> currentSettings.copy(formSectionTitleFontSize = value)
                        }
                        settingsRepository.saveGlobalSettings(newSettings)
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 标题栏按钮配置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与标题栏设置对话框保持完全一致的布局和样式
 */
@Composable
private fun TopAppBarButtonSettingDialog(
    dialogType: TopAppBarButtonDialogType,
    currentSettings: GlobalSettings,
    onDismiss: () -> Unit,
    settingsRepository: SettingsRepository
) {
    val context = LocalContext.current

    // 获取当前值
    val currentValue = when (dialogType) {
        TopAppBarButtonDialogType.CIRCLE_BACKGROUND_SIZE -> currentSettings.topAppBarButtonCircleBackgroundSize
        TopAppBarButtonDialogType.CIRCLE_BACKGROUND_HORIZONTAL_MARGIN -> currentSettings.topAppBarButtonCircleBackgroundHorizontalMargin
        TopAppBarButtonDialogType.CIRCLE_BACKGROUND_RIGHT_MARGIN -> currentSettings.topAppBarButtonCircleBackgroundRightMargin
    }

    var inputText by remember(currentValue) { mutableStateOf(currentValue.toString()) }

    // 获取UI间距配置
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text(dialogType.title) },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            // 只允许数字和负号（用于偏移设置）
                            if (newValue.isEmpty() || newValue.all { it.isDigit() || it == '-' }) {
                                inputText = newValue
                            }
                        },
                        placeholder = "输入${dialogType.title}",
                        settingsRepository = settingsRepository
                    )
                }

                // 范围描述 - 与对话框分割线左边缩进一致，灰字样式与模糊设置对话框保持一致
                Text(
                    text = "范围：${dialogType.range.first}-${dialogType.range.last}（${dialogType.unit}）",
                    color = Color.Gray,
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val value = inputText.toIntOrNull()
                    if (value != null && value in dialogType.range) {
                        // 根据对话框类型调用相应的更新方法
                        val newSettings = when (dialogType) {
                            TopAppBarButtonDialogType.CIRCLE_BACKGROUND_SIZE -> currentSettings.copy(topAppBarButtonCircleBackgroundSize = value)
                            TopAppBarButtonDialogType.CIRCLE_BACKGROUND_HORIZONTAL_MARGIN -> currentSettings.copy(topAppBarButtonCircleBackgroundHorizontalMargin = value)
                            TopAppBarButtonDialogType.CIRCLE_BACKGROUND_RIGHT_MARGIN -> currentSettings.copy(topAppBarButtonCircleBackgroundRightMargin = value)
                        }
                        settingsRepository.saveGlobalSettings(newSettings)
                        onDismiss()
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 背景透明度设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 */
@Composable
private fun BackgroundAlphaDialog(
    currentAlpha: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentAlpha) { mutableStateOf(currentAlpha.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("背景透明度设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 背景透明度输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入背景透明度",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "设置模糊效果下背景颜色的透明度\n范围：0-100%，推荐值：10%",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val alpha = inputText.toIntOrNull()
                    if (alpha != null && alpha in 0..100) {
                        onConfirm(alpha)
                    }
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 组件特定的模糊强度设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 */
@Composable
private fun ComponentBlurIntensityDialog(
    component: BlurComponent,
    componentName: String,
    currentIntensity: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentIntensity) { mutableStateOf(currentIntensity.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("${componentName}模糊强度设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 模糊强度输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入模糊强度",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "设置${componentName}的模糊强度 (0-100)",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    textAlign = TextAlign.Center
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val intensity = inputText.toIntOrNull()?.coerceIn(0, 100) ?: currentIntensity
                    onConfirm(intensity)
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 组件特定的背景透明度设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 */
@Composable
private fun ComponentBackgroundAlphaDialog(
    component: BlurComponent,
    componentName: String,
    currentAlpha: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentAlpha) { mutableStateOf(currentAlpha.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("${componentName}背景透明度设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 背景透明度输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入背景透明度",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "设置${componentName}的背景透明度 (0-100)",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    textAlign = TextAlign.Center
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val alpha = inputText.toIntOrNull()?.coerceIn(0, 100) ?: currentAlpha
                    onConfirm(alpha)
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 标题栏类型设置组件
 * 使用天空蓝主题的下拉选择器，与字重设置项保持完全一致的样式
 */
@Composable
private fun TopAppBarTypeSetting(
    selectedTopAppBarType: String,
    topAppBarTypeDisplayName: String,
    onTopAppBarTypeSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    var expanded by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    // 标题栏类型选项
    val topAppBarTypeOptions = listOf(
        "standard" to "标准顶部应用栏",
        "collapsible" to "可折叠顶部应用栏"
    )

    Box {
        // 使用与字重设置项相同的布局样式 - 整个区域都可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = "标题栏类型",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前类型名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = topAppBarTypeDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp) // 向右偏移与箭头协调
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp) // 向右偏移使其更靠近卡片边缘
                )
            }
        }

        // 条件渲染：只在展开时渲染下拉菜单
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp), // 向右偏移到卡片右侧
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                topAppBarTypeOptions.forEachIndexed { index, (value, displayName) ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中类型显示勾选图标
                                if (value == selectedTopAppBarType) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onTopAppBarTypeSelected(value)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 添加分割线（除了最后一项）
                    if (index < topAppBarTypeOptions.size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 模糊样式下拉菜单组件
 * 用于选择预设材质或自定义模式
 */
@Composable
private fun BlurStyleDropdownMenu(
    title: String,
    selectedBlurStyle: String,
    blurStyleDisplayName: String,
    onBlurStyleSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    var expanded by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    Box {
        // 使用与其他下拉菜单相同的布局样式
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前样式名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = blurStyleDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp)
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp)
                )
            }
        }

        // 条件渲染：只在展开时渲染下拉菜单
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp),
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                val blurStyles = listOf(
                    "preset" to "预设材质",
                    "custom" to "自定义"
                )

                blurStyles.forEachIndexed { index, (style, displayName) ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中样式显示勾选图标
                                if (style == selectedBlurStyle) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onBlurStyleSelected(style)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 在选项之间添加分割线（除了最后一个）
                    if (index < blurStyles.size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 预设材质下拉菜单组件
 * 用于选择具体的预设材质类型
 */
@Composable
private fun PresetMaterialDropdownMenu(
    title: String,
    selectedMaterial: String,
    materialDisplayName: String,
    onMaterialSelected: (String) -> Unit,
    uiSpacingConfig: com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.UISpacingConfiguration
) {
    var expanded by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val themeManager = remember { ThemeManager.getInstance(context) }
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        val themeContext = LocalThemeContext.current
        themeContext.styleConfiguration.cardStyle
    }

    // 获取主题上下文（用于分割线）
    val settingsRepository = remember { SettingsRepository(context) }
    val themeContext = LocalThemeContext.current

    Box {
        // 使用与其他下拉菜单相同的布局样式
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = true }
                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧：设置项标题
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            // 右侧：当前材质名称 + 右箭头
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = materialDisplayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                    modifier = Modifier.offset(x = 12.dp)
                )

                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_sky_blue_arrow_right),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                    modifier = Modifier
                        .size(24.dp)
                        .offset(x = 5.dp)
                )
            }
        }

        // 条件渲染：只在展开时渲染下拉菜单
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false },
                offset = DpOffset(x = 122.dp, y = 11.dp),
                modifier = Modifier
                    .widthIn(min = 200.dp)
                    .background(
                        color = MaterialTheme.colorScheme.surfaceContainerLow,
                        shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
                    ),
                shape = RoundedCornerShape(cardStyle.defaultCornerRadius)
            ) {
                val materials = listOf(
                    "ultraThin" to "超薄",
                    "thin" to "薄",
                    "regular" to "常规",
                    "thick" to "厚"
                )

                materials.forEachIndexed { index, (material, displayName) ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = displayName,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    style = MaterialTheme.typography.bodyMedium,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                    modifier = Modifier.weight(1f)
                                )

                                // 当前选中材质显示勾选图标
                                if (material == selectedMaterial) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        },
                        onClick = {
                            onMaterialSelected(material)
                            expanded = false
                        },
                        colors = MenuDefaults.itemColors(
                            textColor = MaterialTheme.colorScheme.onSurface
                        ),
                        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp)
                    )

                    // 在选项之间添加分割线（除了最后一个）
                    if (index < materials.size - 1) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = (16 + uiSpacingConfig.dividerHorizontalPadding).dp),
                                color = if (uiSpacingConfig.dividerVisible) Color(0x33202020) else Color.Transparent
                            )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 组件特定的模糊半径设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 */
@Composable
private fun ComponentBlurRadiusDialog(
    component: BlurComponent,
    componentName: String,
    currentRadius: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentRadius) { mutableStateOf(currentRadius.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("${componentName}模糊半径设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 模糊半径输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 2)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入模糊半径",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "设置${componentName}的模糊半径 (5-50dp)",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    textAlign = TextAlign.Center
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val radius = inputText.toIntOrNull()?.coerceIn(5, 50) ?: currentRadius
                    onConfirm(radius)
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 组件特定的噪声因子设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 */
@Composable
private fun ComponentNoiseFactorDialog(
    component: BlurComponent,
    componentName: String,
    currentNoiseFactor: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentNoiseFactor) { mutableStateOf(currentNoiseFactor.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfig = uiSpacingConfig

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("${componentName}噪声因子设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 噪声因子输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入噪声因子",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "噪声因子控制模糊效果的纹理噪声强度，范围：0-100\n0 = 无噪声，100 = 最大噪声",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val newNoiseFactor = inputText.toIntOrNull()
                    if (newNoiseFactor != null && newNoiseFactor in 0..100) {
                        onConfirm(newNoiseFactor)
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 组件特定的色调强度设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 */
@Composable
private fun ComponentTintIntensityDialog(
    component: BlurComponent,
    componentName: String,
    currentTintIntensity: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentTintIntensity) { mutableStateOf(currentTintIntensity.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfig = uiSpacingConfig

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("${componentName}色调强度设置") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 色调强度输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 2)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入色调强度",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "色调强度控制模糊效果的色调强度，范围：0-20\n0 = 无色调，10 = 默认强度，20 = 最大强度",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val newTintIntensity = inputText.toIntOrNull()
                    if (newTintIntensity != null && newTintIntensity in 0..20) {
                        onConfirm(newTintIntensity)
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 结果显示延迟时长设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 与小组件更新间隔设置对话框保持完全一致的布局和样式
 */
@Composable
private fun CheckupDisplayDurationDialog(
    currentDuration: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentDuration) { mutableStateOf(currentDuration.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("延迟显示时长") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框 - 延迟显示时长输入，与分割线缩进保持一致
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 2)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入延迟时长（秒）",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "设置体检完成后延迟显示结果的时间，让您有时间欣赏完整的体检动画过程，范围：1-30秒",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val newDuration = inputText.toIntOrNull()
                    if (newDuration != null && newDuration in 1..30) {
                        onConfirm(newDuration)
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 固定体检分数设置对话框
 */
@Composable
private fun FixedCheckupScoreDialog(
    currentScore: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentScore) { mutableStateOf(currentScore.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("固定分数值") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入分数值",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "设置固定的体检分数，范围：0-100分",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val newScore = inputText.toIntOrNull()
                    if (newScore != null && newScore in 0..100) {
                        onConfirm(newScore)
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 水波动效阈值设置对话框
 */
@Composable
private fun WaterWaveThresholdDialog(
    currentThreshold: Int,
    onDismiss: () -> Unit,
    onConfirm: (Int) -> Unit,
    settingsRepository: SettingsRepository
) {
    var inputText by remember(currentThreshold) { mutableStateOf(currentThreshold.toString()) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("水位停止阈值") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // iOS风格的数字输入框
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IOSStyleNumberInput(
                        value = inputText,
                        onValueChange = { newValue ->
                            if (newValue.isEmpty() || (newValue.all { it.isDigit() } && newValue.length <= 3)) {
                                inputText = newValue
                            }
                        },
                        placeholder = "请输入阈值分数",
                        modifier = Modifier.padding(bottom = uiSpacingConfig.settingsItemSpacing.dp),
                        settingsRepository = settingsRepository
                    )
                }

                // 说明文字
                Text(
                    text = "设置水位最高到达的百分比，为水波起伏预留空间。范围：1-100分",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp)
                )
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    val newThreshold = inputText.toIntOrNull()
                    if (newThreshold != null && newThreshold in 1..100) {
                        onConfirm(newThreshold)
                    }
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 导航项显示控制对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 参考图片中的多选对话框布局，支持多个导航项的显示/隐藏控制
 */
@Composable
private fun NavigationItemsVisibilityDialog(
    currentVisibility: Map<String, Boolean>,
    onDismiss: () -> Unit,
    onConfirm: (Map<String, Boolean>) -> Unit,
    settingsRepository: SettingsRepository
) {
    var visibility by remember { mutableStateOf(currentVisibility) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    // 导航项配置
    val navigationItems = listOf(
        "phone_checkup" to "体检",
        "quick_commands" to "指令",
        "command_templates" to "探索",
        "smart_reminders" to "提醒",
        "global_settings" to "设置"
    )

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("导航项显示控制") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // 说明文字
                Text(
                    text = "选择要显示的导航项",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(
                        start = 12.dp,
                        end = 12.dp,
                        bottom = 16.dp
                    )
                )

                // 导航项选择列表
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dialogSpacingConfig.dividerHorizontalPadding.dp),
                    verticalArrangement = Arrangement.spacedBy(0.dp)
                ) {
                    navigationItems.forEachIndexed { index, (key, label) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    visibility = visibility.toMutableMap().apply {
                                        this[key] = !(this[key] ?: true)
                                    }
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = label,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )

                            // 使用圆形选择指示器，参考图片样式
                            Box(
                                modifier = Modifier
                                    .size(20.dp)
                                    .background(
                                        color = if (visibility[key] == true) {
                                            Color(0xFF0A59F7) // 天空蓝色
                                        } else {
                                            Color.Transparent
                                        },
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 2.dp,
                                        color = if (visibility[key] == true) {
                                            Color(0xFF0A59F7)
                                        } else {
                                            Color(0xFFD1D1D6)
                                        },
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                if (visibility[key] == true) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = Color.White,
                                        modifier = Modifier.size(12.dp)
                                    )
                                }
                            }
                        }

                        // 添加分割线（除了最后一项）
                        if (index < navigationItems.size - 1) {
                            androidx.compose.material3.HorizontalDivider(
                                modifier = Modifier.padding(horizontal = 0.dp),
                                thickness = 0.5.dp,
                                color = Color(0x33202020)
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    onConfirm(visibility)
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}

/**
 * 默认启动页面设置对话框
 * 使用天空蓝主题的自定义对话框组件，按照iOS风格设计
 * 参考图片中的单选对话框布局，支持选择默认启动页面
 */
@Composable
private fun DefaultStartupPageDialog(
    currentStartupPage: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit,
    settingsRepository: SettingsRepository
) {
    var selectedPage by remember { mutableStateOf(currentStartupPage) }

    // 获取UI间距配置
    val context = LocalContext.current
    val uiSpacingConfigManager = remember { com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager.getInstance(context, settingsRepository) }
    val uiSpacingConfig = uiSpacingConfigManager.getUISpacingConfiguration()

    // 获取对话框间距配置
    val dialogSpacingConfigManager = remember { DialogSpacingConfigurationManager.getInstance(context, settingsRepository) }
    val dialogSpacingConfig = dialogSpacingConfigManager.getDialogSpacingConfiguration()

    // 页面选项配置
    val pageOptions = listOf(
        "phone_checkup" to "体检",
        "quick_commands" to "指令",
        "command_templates" to "探索",
        "smart_reminders" to "提醒",
        "global_settings" to "设置"
    )

    SkyBlueDialog(
        onDismissRequest = onDismiss,
        settingsRepository = settingsRepository,
        title = { Text("默认启动页面") },
        text = {
            Column(
                modifier = Modifier.padding(vertical = uiSpacingConfig.settingsDescriptionSpacing.dp)
            ) {
                // 说明文字
                Text(
                    text = "选择应用启动时显示的页面",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(
                        start = 12.dp,
                        end = 12.dp,
                        bottom = 16.dp
                    )
                )

                // 页面选择列表
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp),
                    verticalArrangement = Arrangement.spacedBy(0.dp)
                ) {
                    pageOptions.forEachIndexed { index, (key, label) ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    selectedPage = key
                                }
                                .padding(vertical = uiSpacingConfig.settingsItemVerticalPadding.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = label,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                modifier = Modifier.weight(1f)
                            )

                            // 使用圆形选择指示器，参考图片样式
                            Box(
                                modifier = Modifier
                                    .size(20.dp)
                                    .background(
                                        color = if (selectedPage == key) {
                                            Color(0xFF0A59F7) // 天空蓝色
                                        } else {
                                            Color.Transparent
                                        },
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 2.dp,
                                        color = if (selectedPage == key) {
                                            Color(0xFF0A59F7)
                                        } else {
                                            Color(0xFFD1D1D6)
                                        },
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                if (selectedPage == key) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "已选中",
                                        tint = Color.White,
                                        modifier = Modifier.size(12.dp)
                                    )
                                }
                            }
                        }

                        // 添加分割线（除了最后一项）
                        if (index < pageOptions.size - 1) {
                            androidx.compose.material3.HorizontalDivider(
                                modifier = Modifier.padding(horizontal = 0.dp),
                                thickness = 0.5.dp,
                                color = Color(0x33202020)
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            SkyBlueDialogButton(
                text = "确定",
                onClick = {
                    onConfirm(selectedPage)
                },
                isPrimary = true,
                settingsRepository = settingsRepository
            )
        },
        dismissButton = {
            SkyBlueDialogButton(
                text = "取消",
                onClick = onDismiss,
                isPrimary = false,
                settingsRepository = settingsRepository
            )
        }
    )
}
