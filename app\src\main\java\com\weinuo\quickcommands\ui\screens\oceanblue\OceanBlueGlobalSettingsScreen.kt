package com.weinuo.quickcommands.ui.screens.oceanblue

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.provider.Settings
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.size
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import com.weinuo.quickcommands.ui.components.themed.ThemedCard
import com.weinuo.quickcommands.ui.components.themed.ThemedButton
import com.weinuo.quickcommands.ui.components.themed.ThemedTextField
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.system.AppTheme
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.R
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.components.layered.LayeredTopAppBar
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog
import com.weinuo.quickcommands.ui.theme.config.TopAppBarConfig
import com.weinuo.quickcommands.ui.theme.config.TopAppBarStyle
import com.weinuo.quickcommands.utils.ExperimentalFeatureDetector
import com.weinuo.quickcommands.utils.StoragePermissionUtil
import com.weinuo.quickcommands.utils.LanguageManager
import com.weinuo.quickcommands.utils.LocaleHelper
import com.weinuo.quickcommands.utils.withAppLanguage
import com.weinuo.quickcommands.permission.GlobalPermissionManager
import com.weinuo.quickcommands.widget.WidgetUpdateManager
import com.weinuo.quickcommands.storage.UIStateStorageManager
import com.weinuo.quickcommands.ui.activities.AppSelectionActivity


/**
 * 海洋蓝主题专用 - 全局设置屏幕
 *
 * 特点：
 * - 分层设计风格
 * - 使用标准Material 3组件
 * - 清晰的视觉层次
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OceanBlueGlobalSettingsScreen(
    settingsRepository: SettingsRepository,
    experimentalFeatureDetector: ExperimentalFeatureDetector? = null
) {
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior(rememberTopAppBarState())
    val currentSettings by settingsRepository.globalSettings.collectAsState()

    // 小组件更新设置
    var widgetUpdateEnabled by remember(currentSettings) {
        mutableStateOf(currentSettings.widgetUpdateEnabled)
    }
    var widgetUpdateInterval by remember(currentSettings) {
        mutableStateOf(currentSettings.widgetUpdateInterval.toString())
    }
    var widgetUpdateIntervalError by remember { mutableStateOf(false) }

    // 上下文
    val context = LocalContext.current

    // 主题管理器
    val themeManager = remember { ThemeManager.getInstance(context) }

    // UI状态管理器
    val uiStateManager = remember { UIStateStorageManager(context) }

    // 优化保护名单状态
    var protectionListApps by remember { mutableStateOf(emptyList<com.weinuo.quickcommands.model.SimpleAppInfo>()) }

    // 应用选择ActivityResultLauncher
    val appSelectionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(AppSelectionActivity.RESULT_SELECTED_APPS)
            if (resultKey != null) {
                val loadedApps = uiStateManager.loadAppListState(resultKey, "selected_apps")
                if (loadedApps.isNotEmpty()) {
                    protectionListApps = loadedApps
                    // 保存到持久化存储
                    uiStateManager.saveAppListState("optimization_protection_list", "selected_apps", loadedApps)
                }
                // 清理临时存储
                uiStateManager.clearAppListState(resultKey, "selected_apps")
            }
        }
    }

    // 初始化时加载保存的应用列表
    LaunchedEffect(Unit) {
        protectionListApps = uiStateManager.loadAppListState("optimization_protection_list", "selected_apps")
    }



    // 实验性功能状态
    var experimentalFeaturesEnabled by remember(currentSettings) {
        mutableStateOf(currentSettings.experimentalFeaturesEnabled)
    }

    // 语言设置状态 - 使用局部状态管理，避免影响其他设置项
    var selectedLanguage by remember(currentSettings) {
        mutableStateOf(currentSettings.appLanguage)
    }

    // 语言显示名称 - 使用derivedStateOf减少重组
    val languageDisplayName by remember {
        derivedStateOf {
            when (selectedLanguage) {
                LanguageManager.LANGUAGE_SYSTEM -> context.withAppLanguage().getString(R.string.language_system_default)
                LanguageManager.LANGUAGE_CHINESE -> context.withAppLanguage().getString(R.string.language_chinese)
                LanguageManager.LANGUAGE_ENGLISH -> context.withAppLanguage().getString(R.string.language_english)
                else -> selectedLanguage
            }
        }
    }

    // 即时保存函数
    fun saveSettingsImmediately() {
        val newSettings = currentSettings.copy(
            widgetUpdateEnabled = widgetUpdateEnabled,
            widgetUpdateInterval = widgetUpdateInterval.toIntOrNull() ?: 24,
            experimentalFeaturesEnabled = experimentalFeaturesEnabled,
            appLanguage = selectedLanguage
        )

        // 检查小组件设置是否有变化
        val widgetSettingsChanged = currentSettings.widgetUpdateEnabled != widgetUpdateEnabled ||
                currentSettings.widgetUpdateInterval != (widgetUpdateInterval.toIntOrNull() ?: 24)

        // 检查语言设置是否有变化
        val languageChanged = currentSettings.appLanguage != selectedLanguage

        // 批量保存所有设置，避免多次SharedPreferences写操作
        settingsRepository.saveGlobalSettings(newSettings)

        // 如果语言设置有变化，更新LanguageManager缓存并清理LocaleHelper缓存
        if (languageChanged) {
            LanguageManager.setLanguage(context, selectedLanguage)
            LocaleHelper.clearCache()
        }

        // 如果小组件设置有变化，立即更新小组件
        if (widgetSettingsChanged) {
            WidgetUpdateManager.forceUpdateAllWidgets(context)
        }
    }

    Scaffold(
        topBar = {
            LayeredTopAppBar(
                config = TopAppBarConfig(
                    title = stringResource(R.string.nav_global_settings),
                    style = TopAppBarStyle.STANDARD,
                    collapsible = true,
                    scrollBehavior = scrollBehavior,
                    windowInsets = WindowInsets.statusBars
                )
            )
        },
        // 设置为空的WindowInsets，让内容可以延伸到系统栏区域
        contentWindowInsets = WindowInsets(0, 0, 0, 0),
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection)
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(start = 16.dp, end = 16.dp, top = 8.dp, bottom = 88.dp) // 为底部导航栏留出空间
            ) {

                // 外观主题设置
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "外观主题",
                            style = MaterialTheme.typography.titleMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            verticalAlignment = Alignment.Top,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(end = 16.dp)
                            ) {
                                Text(
                                    text = "选择应用外观风格",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                                Text(
                                    text = "海洋蓝为简洁风格，天空蓝为整合设计",
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }

                            // 主题选择下拉菜单
                            ThemeDropdownMenu()
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 悬浮加速球设置
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "悬浮加速球",
                            style = MaterialTheme.typography.titleMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            verticalAlignment = Alignment.Top,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(end = 16.dp)
                            ) {
                                Text(
                                    text = "启用悬浮加速球",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                                Text(
                                    text = "在屏幕上显示悬浮的内存监控和清理工具",
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                            Switch(
                                checked = currentSettings.floatingAcceleratorEnabled,
                                onCheckedChange = { enabled ->
                                    val newSettings = currentSettings.copy(floatingAcceleratorEnabled = enabled)
                                    settingsRepository.saveGlobalSettings(newSettings)
                                }
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 优化保护名单设置
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "优化保护名单",
                            style = MaterialTheme.typography.titleMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            verticalAlignment = Alignment.Top,
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    // 跳转到应用选择界面
                                    val resultKey = "optimization_protection_list_${System.currentTimeMillis()}"
                                    val intent = AppSelectionActivity.createMultiSelectionIntent(
                                        context = context,
                                        selectedAppPackageNames = protectionListApps.map { it.packageName },
                                        resultKey = resultKey
                                    )
                                    appSelectionLauncher.launch(intent)
                                }
                        ) {
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(end = 16.dp)
                            ) {
                                Text(
                                    text = "优化保护名单",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                                Text(
                                    text = if (protectionListApps.isEmpty()) {
                                        "选择需要保护的应用，避免被系统优化"
                                    } else {
                                        "已选择${protectionListApps.size}个应用"
                                    },
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                            Icon(
                                imageVector = Icons.Default.ChevronRight,
                                contentDescription = null,
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // 小组件更新设置
                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.widget_update_settings),
                            style = MaterialTheme.typography.titleMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            verticalAlignment = Alignment.Top,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(end = 16.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.widget_update_enabled),
                                    style = MaterialTheme.typography.bodyLarge
                                )
                                Text(
                                    text = stringResource(R.string.widget_update_enabled_description),
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                            Switch(
                                checked = widgetUpdateEnabled,
                                onCheckedChange = {
                                    // 小组件更新设置无需Shizuku权限，直接执行并立即保存
                                    widgetUpdateEnabled = it
                                    saveSettingsImmediately()
                                },
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }

                        // 只有当小组件更新启用时才显示更新间隔设置
                        if (widgetUpdateEnabled) {
                            Spacer(modifier = Modifier.height(16.dp))

                            ThemedTextField(
                                value = widgetUpdateInterval,
                                onValueChange = {
                                    widgetUpdateInterval = it
                                    widgetUpdateIntervalError = !isValidNumber(it) || (it.toIntOrNull() ?: 0) < 1
                                },
                                label = stringResource(R.string.widget_update_interval),
                                supportingText = stringResource(R.string.widget_update_interval_hint),
                                isError = widgetUpdateIntervalError,
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .onFocusChanged { focusState ->
                                        // 失去焦点时自动保存（如果输入有效）
                                        if (!focusState.isFocused && !widgetUpdateIntervalError && widgetUpdateInterval.isNotEmpty()) {
                                            saveSettingsImmediately()
                                        }
                                    }
                            )

                            if (widgetUpdateIntervalError) {
                                Text(
                                    text = stringResource(R.string.widget_update_interval_error),
                                    color = MaterialTheme.colorScheme.error,
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(start = 16.dp, top = 4.dp)
                                )
                            }
                        }

                        // 手动更新按钮
                        Spacer(modifier = Modifier.height(16.dp))

                        Column(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = stringResource(R.string.manual_widget_update),
                                style = MaterialTheme.typography.bodyLarge
                            )

                            Spacer(modifier = Modifier.height(4.dp))

                            Text(
                                text = stringResource(R.string.manual_widget_update_description),
                                style = MaterialTheme.typography.bodySmall
                            )

                            Spacer(modifier = Modifier.height(12.dp))

                            TextButton(
                                onClick = {
                                    WidgetUpdateManager.forceUpdateAllWidgets(context)
                                    Toast.makeText(context, context.getString(R.string.widget_update_completed), Toast.LENGTH_SHORT).show()
                                },
                                modifier = Modifier.align(Alignment.End)
                            ) {
                                Text(
                                    text = stringResource(R.string.update_now),
                                    color = MaterialTheme.colorScheme.primary
                                )
                            }
                        }
                    }
                }

                // 语言设置卡片
                Spacer(modifier = Modifier.height(12.dp))

                ThemedCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.language_settings),
                            style = MaterialTheme.typography.titleMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            verticalAlignment = Alignment.Top,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(end = 16.dp)
                            ) {
                                Text(
                                    text = stringResource(R.string.language_selection_title),
                                    style = MaterialTheme.typography.bodyLarge
                                )
                                Text(
                                    text = stringResource(R.string.language_settings_description),
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }

                            // 语言选择下拉菜单
                            LanguageDropdownMenu(
                                selectedLanguage = selectedLanguage,
                                languageDisplayName = languageDisplayName,
                                onLanguageSelected = { language ->
                                    val previousLanguage = selectedLanguage
                                    selectedLanguage = language
                                    saveSettingsImmediately()

                                    // 如果语言确实发生了变化，显示重启提示
                                    if (previousLanguage != language) {
                                        Toast.makeText(
                                            context,
                                            "语言设置已保存，重启应用后生效",
                                            Toast.LENGTH_LONG
                                        ).show()
                                    }
                                }
                            )
                        }
                    }
                }

                // 实验性功能卡片 - 只有在解锁状态时才显示
                if (currentSettings.experimentalFeaturesUnlocked) {
                    Spacer(modifier = Modifier.height(12.dp))

                    ThemedCard(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.experimental_features),
                                style = MaterialTheme.typography.titleMedium
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Row(
                                verticalAlignment = Alignment.Top,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Column(
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(end = 16.dp)
                                ) {
                                    Text(
                                        text = stringResource(R.string.experimental_features_enabled),
                                        style = MaterialTheme.typography.bodyLarge
                                    )
                                    Text(
                                        text = stringResource(R.string.experimental_features_description),
                                        style = MaterialTheme.typography.bodySmall,
                                        modifier = Modifier.padding(top = 4.dp)
                                    )
                                }
                                Switch(
                                    checked = experimentalFeaturesEnabled,
                                    onCheckedChange = {
                                        experimentalFeaturesEnabled = it
                                        saveSettingsImmediately()
                                    },
                                    modifier = Modifier.padding(top = 4.dp)
                                )
                            }
                        }
                    }
                }
            }

            // 实验性功能激活区域 - 左下角透明点击区域
            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(32.dp)
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null
                    ) {
                        // 处理左下角白色背景区域点击
                        experimentalFeatureDetector?.handleClick(ExperimentalFeatureDetector.ClickTarget.BACKGROUND_AREA)
                    }
            )
        }
    }






}

/**
 * 验证输入是否为有效数字
 */
private fun isValidNumber(input: String): Boolean {
    return input.isEmpty() || input.toIntOrNull() != null
}

/**
 * 检查是否有使用情况访问权限
 */
private fun hasUsageStatsPermission(context: Context): Boolean {
    val appOps = context.getSystemService(Context.APP_OPS_SERVICE) as android.app.AppOpsManager
    val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        appOps.unsafeCheckOpNoThrow(
            android.app.AppOpsManager.OPSTR_GET_USAGE_STATS,
            android.os.Process.myUid(),
            context.packageName
        )
    } else {
        appOps.checkOpNoThrow(
            android.app.AppOpsManager.OPSTR_GET_USAGE_STATS,
            android.os.Process.myUid(),
            context.packageName
        )
    }
    return mode == android.app.AppOpsManager.MODE_ALLOWED
}

/**
 * 打开使用情况访问权限设置页面
 */
private fun openUsageStatsSettings(context: Context) {
    val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
    context.startActivity(intent)
}

/**
 * 语言下拉菜单组件
 *
 * 性能优化特性：
 * 1. 局部状态管理，只有下拉菜单会重组
 * 2. 条件渲染，下拉菜单只在展开时渲染
 * 3. 使用remember减少不必要的重组
 * 4. 紧凑的UI设计，与其他设置项保持一致
 */
@Composable
private fun LanguageDropdownMenu(
    selectedLanguage: String,
    languageDisplayName: String,
    onLanguageSelected: (String) -> Unit
) {
    // 下拉菜单展开状态 - 局部状态，不影响其他组件
    var expanded by remember { mutableStateOf(false) }

    // 上下文 - 用于获取本地化字符串
    val context = LocalContext.current

    Box {
        // 使用紧凑的按钮样式，与其他设置项保持一致
        OutlinedButton(
            onClick = { expanded = true },
            modifier = Modifier
                .padding(top = 4.dp)
                .widthIn(min = 120.dp)
        ) {
            Text(
                text = languageDisplayName,
                style = MaterialTheme.typography.bodyMedium
            )
            Spacer(modifier = Modifier.width(8.dp))
            Icon(
                imageVector = Icons.Default.ExpandMore,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
        }

        // 条件渲染：只在展开时渲染下拉菜单，减少性能开销
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                // 系统默认
                DropdownMenuItem(
                    text = {
                        Text(context.withAppLanguage().getString(R.string.language_system_default))
                    },
                    onClick = {
                        onLanguageSelected(LanguageManager.LANGUAGE_SYSTEM)
                        expanded = false
                    }
                )

                // 中文
                DropdownMenuItem(
                    text = {
                        Text(context.withAppLanguage().getString(R.string.language_chinese))
                    },
                    onClick = {
                        onLanguageSelected(LanguageManager.LANGUAGE_CHINESE)
                        expanded = false
                    }
                )

                // English
                DropdownMenuItem(
                    text = {
                        Text(context.withAppLanguage().getString(R.string.language_english))
                    },
                    onClick = {
                        onLanguageSelected(LanguageManager.LANGUAGE_ENGLISH)
                        expanded = false
                    }
                )
            }
        }
    }
}

/**
 * 主题下拉菜单组件
 *
 * 与语言下拉菜单保持一致的设计风格
 */
@Composable
private fun ThemeDropdownMenu() {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val currentTheme by themeManager.currentTheme

    // 下拉菜单展开状态
    var expanded by remember { mutableStateOf(false) }

    Box {
        // 使用紧凑的按钮样式，与其他设置项保持一致
        OutlinedButton(
            onClick = { expanded = true },
            modifier = Modifier
                .padding(top = 4.dp)
                .widthIn(min = 120.dp)
        ) {
            Text(
                text = currentTheme.displayName,
                style = MaterialTheme.typography.bodyMedium
            )
            Spacer(modifier = Modifier.width(8.dp))
            Icon(
                imageVector = Icons.Default.ExpandMore,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
        }

        // 条件渲染：只在展开时渲染下拉菜单
        if (expanded) {
            DropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                AppTheme.values().forEach { theme ->
                    DropdownMenuItem(
                        text = {
                            Text(theme.displayName)
                        },
                        onClick = {
                            themeManager.setTheme(theme)
                            expanded = false
                        }
                    )
                }
            }
        }
    }
}
