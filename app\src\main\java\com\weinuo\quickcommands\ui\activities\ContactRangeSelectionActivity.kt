package com.weinuo.quickcommands.ui.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton

/**
 * 联系人范围选择Activity
 */
class ContactRangeSelectionActivity : ComponentActivity() {

    companion object {
        const val EXTRA_CURRENT_SELECTION = "current_selection"
        const val RESULT_SELECTION = "selection_result"

        fun startForResult(context: Context, currentSelection: String = "任何联系人") {
            val intent = Intent(context, ContactRangeSelectionActivity::class.java).apply {
                putExtra(EXTRA_CURRENT_SELECTION, currentSelection)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val currentSelection = intent.getStringExtra(EXTRA_CURRENT_SELECTION) ?: "任何联系人"

        setContent {
            QuickCommandsTheme {
                ContactRangeSelectionScreen(
                    currentSelection = currentSelection,
                    onSelectionChanged = { selection ->
                        // 返回选择结果
                        val resultIntent = Intent().apply {
                            putExtra(RESULT_SELECTION, selection)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onNavigateBack = {
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 联系人范围选择界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactRangeSelectionScreen(
    currentSelection: String,
    onSelectionChanged: (String) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 联系人范围选项
    val rangeOptions = listOf(
        "任何联系人",
        "陌生号码",
        "指定联系人",
        "指定分组",
        "任何号码",
        "指定号码"
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "联系人范围",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {
            items(rangeOptions) { option ->
                ContactRangeOptionItem(
                    title = option,
                    isSelected = option == currentSelection,
                    onClick = {
                        onSelectionChanged(option)
                    }
                )
            }
        }
    }
}

/**
 * 联系人范围选项项目
 */
@Composable
fun ContactRangeOptionItem(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceContainerLow
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )

            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "已选择",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
    
    Spacer(modifier = Modifier.height(8.dp))
}
