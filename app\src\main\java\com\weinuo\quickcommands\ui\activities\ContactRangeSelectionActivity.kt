package com.weinuo.quickcommands.ui.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.ui.components.themed.ThemedCard
import com.weinuo.quickcommands.ui.theme.config.DividerConfig
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.text.font.FontWeight

/**
 * 联系人范围选择Activity
 */
class ContactRangeSelectionActivity : ComponentActivity() {

    companion object {
        const val EXTRA_CURRENT_SELECTION = "current_selection"
        const val RESULT_SELECTION = "selection_result"

        fun startForResult(context: Context, currentSelection: String = "任何联系人") {
            val intent = Intent(context, ContactRangeSelectionActivity::class.java).apply {
                putExtra(EXTRA_CURRENT_SELECTION, currentSelection)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val currentSelection = intent.getStringExtra(EXTRA_CURRENT_SELECTION) ?: "任何联系人"

        setContent {
            QuickCommandsTheme {
                ContactRangeSelectionScreen(
                    currentSelection = currentSelection,
                    onSelectionChanged = { selection ->
                        // 返回选择结果
                        val resultIntent = Intent().apply {
                            putExtra(RESULT_SELECTION, selection)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onNavigateBack = {
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 联系人范围选择界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ContactRangeSelectionScreen(
    currentSelection: String,
    onSelectionChanged: (String) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 联系人范围选项
    val rangeOptions = listOf(
        "任何联系人",
        "陌生号码",
        "指定联系人",
        "指定分组",
        "任何号码",
        "指定号码"
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "联系人范围",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                ContactRangeSelectionCard(
                    rangeOptions = rangeOptions,
                    currentSelection = currentSelection,
                    onSelectionChanged = onSelectionChanged
                )
            }
        }
    }
}

/**
 * 联系人范围选择卡片
 */
@Composable
fun ContactRangeSelectionCard(
    rangeOptions: List<String>,
    currentSelection: String,
    onSelectionChanged: (String) -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取UI间距配置（仅在天空蓝主题下使用）
    val uiSpacingConfig = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        val globalSettings by settingsRepository.globalSettings.collectAsState()
        globalSettings
    } else {
        null
    }

    // 使用ThemedCard替代单独的Card，保持与收到短信配置界面一致的样式
    ThemedCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {
            rangeOptions.forEachIndexed { index, option ->
                // 选项内容行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = null // 移除涟漪效果，保持iOS风格
                        ) { onSelectionChanged(option) }
                        .padding(
                            horizontal = cardStyle.defaultHorizontalPadding,
                            vertical = uiSpacingConfig?.uiSettingsItemVerticalPadding?.dp ?: cardStyle.settingsVerticalPadding
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧标题
                    Text(
                        text = option,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.weight(1f)
                    )

                    // 右侧选中状态指示
                    if (option == currentSelection) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "已选择",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                // 分割线（除了最后一项）
                if (index < rangeOptions.size - 1) {
                    // 主题感知的分割线显示逻辑
                    val shouldShowDivider = remember(themeManager.getCurrentThemeId()) {
                        when (themeManager.getCurrentThemeId()) {
                            "sky_blue" -> true  // 天空蓝主题：显示分割线
                            "ocean_blue" -> false // 海洋蓝主题：隐藏分割线
                            else -> true        // 其他主题：显示分割线
                        }
                    }

                    if (shouldShowDivider) {
                        themeContext.componentFactory.createDivider()(
                            DividerConfig(
                                modifier = Modifier.padding(horizontal = uiSpacingConfig?.uiDividerHorizontalPadding?.dp ?: 0.dp),
                                color = MaterialTheme.colorScheme.outlineVariant.copy(alpha = 0.3f)
                            )
                        )
                    }
                }
            }
        }
    }
}
