package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.ConfigurationCardItem
import com.weinuo.quickcommands.ui.components.ExpandableConfigurationCard
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
import com.weinuo.quickcommands.model.CommunicationStateType

// 创建 CompositionLocal 来提供 NavController
val LocalNavController = staticCompositionLocalOf<NavController?> { null }

/**
 * 通用详细配置界面组件
 *
 * 使用Scaffold + LazyColumn + 可展开卡片架构的通用配置界面。
 * 支持任意类型的配置项列表，提供统一的标题显示和确定按钮，
 * 支持编辑模式（预展开指定卡片），统一的配置完成和取消逻辑。
 *
 * @param T 配置项操作类型的泛型参数
 * @param title 界面标题
 * @param configurationItems 配置项列表
 * @param initialExpandedItemId 初始展开的配置项ID（编辑模式使用）
 * @param initialConfigObject 初始配置对象（编辑模式使用）
 * @param onConfigurationComplete 配置完成回调，传递配置结果
 * @param onDismiss 取消回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun <T> DetailConfigurationScreen(
    title: String,
    configurationItems: List<ConfigurationCardItem<T>>,
    initialExpandedItemId: String? = null,
    initialConfigObject: Any? = null,
    onConfigurationComplete: (Any) -> Unit,
    onDismiss: () -> Unit,
    navController: NavController? = null
) {
    val context = LocalContext.current

    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的标题字重和字体大小
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.topAppBarTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }

    // 展开状态管理 - 同时只能展开一个卡片，使用 rememberSaveable 保持状态
    var expandedCardId by rememberSaveable { mutableStateOf(initialExpandedItemId) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = title,
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onDismiss)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onDismiss) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        // 如果提供了 navController，则通过 CompositionLocalProvider 提供给子组件
        val content = @Composable {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                contentPadding = PaddingValues(vertical = 8.dp)
            ) {
                items(configurationItems) { item ->
                    ExpandableConfigurationCard<Any>(
                        title = item.title,
                        description = item.description,
                        operationType = item.operationType as Any,
                        isExpanded = expandedCardId == item.id,
                        onExpandToggle = {
                            expandedCardId = if (expandedCardId == item.id) {
                                null // 收起当前卡片
                            } else {
                                item.id // 展开新卡片，自动收起其他卡片
                            }
                        },
                        onCardClick = {
                            // 点击卡片跳转到新界面
                            // 检查是否为通信状态配置项
                            if (item.operationType is CommunicationStateType) {
                                val communicationType = item.operationType as CommunicationStateType
                                CommunicationStateDetailConfigActivity.startForCreate(
                                    context,
                                    communicationType
                                )
                            } else {
                                // 其他类型暂时使用展开逻辑
                                expandedCardId = if (expandedCardId == item.id) {
                                    null // 收起当前卡片
                                } else {
                                    item.id // 展开新卡片，自动收起其他卡片
                                }
                            }
                        },
                        permissionRequired = item.permissionRequired
                    ) {
                        // 卡片内容区域 - 根据是否有初始配置对象选择合适的内容组件
                        if (initialConfigObject != null && item.editableContent != null) {
                            // 编辑模式：使用支持初始配置对象的内容组件
                            item.editableContent(item.operationType, initialConfigObject) { result ->
                                // 配置完成回调 - 直接完成配置，不需要点击右上角按钮
                                onConfigurationComplete(result)
                            }
                        } else {
                            // 新建模式：使用普通的内容组件
                            item.content(item.operationType) { result ->
                                android.util.Log.d("DetailConfigurationContent", "配置项完成回调被调用: itemId=${item.id}, result=$result")
                                // 配置完成回调 - 直接完成配置，不需要点击右上角按钮
                                onConfigurationComplete(result)
                            }
                        }
                    }
                }

                // 底部间距
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }

        // 如果提供了 navController，则通过 CompositionLocalProvider 提供给子组件
        if (navController != null) {
            CompositionLocalProvider(LocalNavController provides navController) {
                content()
            }
        } else {
            content()
        }
    }
}

/**
 * 界面结构示意：
 * ┌─────────────────────────────────┐
 * │ TopAppBar: 标题 + 返回 + 确定    │
 * ├─────────────────────────────────┤
 * │ LazyColumn:                     │
 * │  ├─ ExpandableConfigurationCard │
 * │  │  ├─ 标题 + 描述 + 展开按钮    │
 * │  │  └─ [展开时] 配置内容区域     │
 * │  ├─ ExpandableConfigurationCard │
 * │  │  ├─ 标题 + 描述 + 展开按钮    │
 * │  │  └─ [展开时] 配置内容区域     │
 * │  └─ ...                         │
 * └─────────────────────────────────┘
 */

/**
 * 详细配置内容组件（不带标题栏）
 *
 * 纯内容组件，不包含Scaffold和TopAppBar，适用于已有标题栏的Activity中使用。
 * 支持任意类型的配置项列表，提供统一的可展开卡片架构，
 * 支持编辑模式（预展开指定卡片），统一的配置完成逻辑。
 *
 * @param T 配置项操作类型的泛型参数
 * @param configurationItems 配置项列表
 * @param initialExpandedItemId 初始展开的配置项ID（编辑模式使用）
 * @param initialConfigObject 初始配置对象（编辑模式使用）
 * @param onConfigurationComplete 配置完成回调，传递配置结果
 * @param navController 导航控制器（可选）
 */
@Composable
fun <T> DetailConfigurationContent(
    configurationItems: List<ConfigurationCardItem<T>>,
    initialExpandedItemId: String? = null,
    initialConfigObject: Any? = null,
    onConfigurationComplete: (Any) -> Unit,
    navController: NavController? = null
) {
    // 展开状态管理 - 同时只能展开一个卡片，使用 rememberSaveable 保持状态
    var expandedCardId by rememberSaveable { mutableStateOf(initialExpandedItemId) }

    val context = LocalContext.current

    // 如果提供了 navController，则通过 CompositionLocalProvider 提供给子组件
    val content = @Composable {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            items(configurationItems) { item ->
                ExpandableConfigurationCard<Any>(
                    title = item.title,
                    description = item.description,
                    operationType = item.operationType as Any,
                    isExpanded = expandedCardId == item.id,
                    onExpandToggle = {
                        expandedCardId = if (expandedCardId == item.id) {
                            null // 收起当前卡片
                        } else {
                            item.id // 展开新卡片，自动收起其他卡片
                        }
                    },
                    onCardClick = {
                        // 点击卡片跳转到新界面
                        // 检查是否为通信状态配置项
                        if (item.operationType is CommunicationStateType) {
                            val communicationType = item.operationType as CommunicationStateType
                            CommunicationStateDetailConfigActivity.startForCreate(
                                context,
                                communicationType
                            )
                        } else {
                            // 其他类型暂时使用展开逻辑
                            expandedCardId = if (expandedCardId == item.id) {
                                null // 收起当前卡片
                            } else {
                                item.id // 展开新卡片，自动收起其他卡片
                            }
                        }
                    },
                    permissionRequired = item.permissionRequired
                ) {
                    // 卡片内容区域 - 根据是否有初始配置对象选择合适的内容组件
                    if (initialConfigObject != null && item.editableContent != null) {
                        // 编辑模式：使用支持初始配置对象的内容组件
                        item.editableContent(item.operationType, initialConfigObject) { result ->
                            // 配置完成回调 - 直接完成配置，不需要点击右上角按钮
                            onConfigurationComplete(result)
                        }
                    } else {
                        // 新建模式：使用普通的内容组件
                        item.content(item.operationType) { result ->
                            // 配置完成回调 - 直接完成配置，不需要点击右上角按钮
                            onConfigurationComplete(result)
                        }
                    }
                }
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }

    // 如果提供了 navController，则通过 CompositionLocalProvider 提供给子组件
    if (navController != null) {
        CompositionLocalProvider(LocalNavController provides navController) {
            content()
        }
    } else {
        content()
    }
}
