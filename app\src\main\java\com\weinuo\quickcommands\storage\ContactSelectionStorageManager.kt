package com.weinuo.quickcommands.storage

import com.weinuo.quickcommands.utils.ContactsHelper

/**
 * 联系人选择存储管理器
 * 
 * 专门用于管理联系人选择相关的临时状态数据
 * 提供联系人和联系人分组的选择结果存储功能
 */
object ContactSelectionStorageManager {
    
    // 联系人选择相关存储
    private val selectedContactsMap = mutableMapOf<String, List<ContactsHelper.ContactInfo>>()
    
    /**
     * 存储选中的联系人列表
     */
    fun storeSelectedContacts(key: String, contacts: List<ContactsHelper.ContactInfo>) {
        selectedContactsMap[key] = contacts
    }

    /**
     * 获取选中的联系人列表
     */
    fun getSelectedContacts(key: String): List<ContactsHelper.ContactInfo> {
        return selectedContactsMap[key] ?: emptyList()
    }

    /**
     * 清除选中的联系人列表
     */
    fun clearSelectedContacts(key: String) {
        selectedContactsMap.remove(key)
    }

    // 联系人分组选择相关存储
    private val selectedContactGroupMap = mutableMapOf<String, ContactsHelper.ContactGroup>()

    /**
     * 存储选中的联系人分组
     */
    fun storeSelectedContactGroup(key: String, group: ContactsHelper.ContactGroup) {
        selectedContactGroupMap[key] = group
    }

    /**
     * 获取选中的联系人分组
     */
    fun getSelectedContactGroup(key: String): ContactsHelper.ContactGroup? {
        return selectedContactGroupMap[key]
    }

    /**
     * 清除选中的联系人分组
     */
    fun clearSelectedContactGroup(key: String) {
        selectedContactGroupMap.remove(key)
    }
    
    /**
     * 清除所有联系人选择数据
     */
    fun clearAll() {
        selectedContactsMap.clear()
        selectedContactGroupMap.clear()
    }
}
