-- Merging decision tree log ---
provider#rikka.shizuku.ShizukuProvider
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:382:9-388:82
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:385:13-35
	android:authorities
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:384:13-59
	android:multiprocess
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:387:13-41
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:386:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:388:13-79
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:383:13-57
provider#androidx.core.content.FileProvider
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:535:9-543:20
	android:grantUriPermissions
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:539:13-47
	android:authorities
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:537:13-64
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:538:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:536:13-62
manifest
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:2:1-546:12
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:2:1-546:12
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:2:1-546:12
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:2:1-546:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaadb670fa6ca1f2c5a47855923454a7\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff597674a64920f525161048cb2bedc7\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfead65ab0070d3a5c6953a3123d6569\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd4296f3379786dc23425ecfd7130bda\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b00bc502ebac934934628c5371eb11c\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [dev.chrisbanes.haze:haze-materials-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967dbd860d89950624e0783b84d2948b\transformed\haze-materials-release\AndroidManifest.xml:2:1-7:12
MERGED from [dev.chrisbanes.haze:haze-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\224d0965d174a3d80cb3c280827d76f6\transformed\haze-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbe454da14384bb37833dcce63330c70\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad2279977a0ab9a9faf3d3cd6fc86c57\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08c9225172fa96963d92686bc80c5e0d\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c36b8cfd8af20acf52fd20004c02f0b3\transformed\material-ripple\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2368c71bd8b2fc1d6b9fcb9e81292ba7\transformed\animation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dea914038927ff52700e9f966bf73df2\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f503a90f72cbab575267a43a3d872ea8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\708ad0d2216a5a4c7b8c8fa24fa83ad6\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\342a711617df3294d50f3bbed1b43a2a\transformed\ui-backhandler-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb470c2bb9bb72c2a693b436558d35ae\transformed\ui-tooling-preview\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\950570e248363716e52dbb1ed2521b7d\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948e4c45da24d54e414534fe9f255021\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd680ebde1bb8e519f1434c60228fb73\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09fc0498259161a6170aac6fff13c700\transformed\ui-geometry\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a24334a9e370adf40f880ddbdf99bd1\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\698e5422ce58bab0678db2e269f4f608\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91ab27a6fa87b1fe11e96fb74e707ad\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76e94506b8e1b5f2623e7170a4659e66\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6aeeaf2ff80bf712f2b27bd52fe7dcc\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06f299a7430b89abd87e700959100d99\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\726a3fae8cb1d143e422f6a4eab9f297\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be4f9c0dfec74063ff30fa08a680dd9\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\576f52a3554e7648cf6b75e678e147f1\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b7e2babe290c28fce233716d8dc30e9\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da0e8e9b02353ac2aa7e20fc8b08751\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c3cfe4d2b3a318dc0da05d36ddb2438\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\194dfe6f58810a08411bb9e62e3f1f5f\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de8c3361cfa36875b3f5a93145f7d0c\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e109b7321dece0f02b118f7abb21702\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c113be44b87c52c5b16adf2cbe56d5c7\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bca9768a5df952a51293a04aaab9d5\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7eaf11704a7bef50ed7e94aaf38b22\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\245ddc52bcc53f88b16e61cd6a9cc20c\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f703f5e37e09a1bf226bd79386dc674\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58ff5ea23647d58c7bc82fa2455ad617\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ead721738e64af23153ea0abb53ae251\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbaf997ae37e03272ee78df9992f8934\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5ebbf88ef650ff58cf781bd71cd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5632ccc8165adbb75e6e26adc7a4728e\transformed\tracing-ktx-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9055cc13851f5b771be299dccd8bdd19\transformed\tracing\AndroidManifest.xml:17:1-22:12
MERGED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:2:1-16:12
MERGED from [dev.rikka.shizuku:api:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99762b566a13ea95b9c52939a4e42cab\transformed\api-13.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [dev.rikka.shizuku:shared:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f773e4b67745be34e493b38028a4378c\transformed\shared-13.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923208cb488569d83b984d91e48f74d5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82131d01a71b48189bb9697d4f3f129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\968b4e2f78df6ff10b2d5223881906ae\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [dev.rikka.shizuku:aidl:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ff1e83b9d7cd53a3c741fa73e69ec1d\transformed\aidl-13.1.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:6:5-7:53
	tools:ignore
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:7:9-50
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:8:5-9:47
	tools:ignore
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:9:9-44
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:8:22-75
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:14:5-15:40
	tools:ignore
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:15:9-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:14:22-79
uses-permission#com.android.launcher.permission.INSTALL_SHORTCUT
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:16:5-88
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:16:22-85
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:17:5-78
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:17:22-75
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:18:5-68
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:18:22-65
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:19:5-75
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:19:22-72
uses-permission#android.permission.VIBRATE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:20:5-66
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:20:22-63
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:23:5-80
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:23:22-77
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:24:5-85
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:24:22-82
uses-permission#android.permission.INTERNET
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:27:5-67
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:27:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:28:5-79
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:28:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:29:5-76
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:29:22-73
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:32:5-77
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:32:22-74
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:35:5-84
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:35:22-81
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:38:5-77
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:38:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:41:5-87
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:41:22-84
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:44:5-79
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:44:22-76
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:45:5-74
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:45:22-71
uses-permission#android.permission.READ_CALL_LOG
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:48:5-72
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:48:22-69
uses-permission#android.permission.WRITE_CALL_LOG
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:49:5-73
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:49:22-70
uses-permission#android.permission.CALL_PHONE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:50:5-69
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:50:22-66
uses-permission#android.permission.MANAGE_OWN_CALLS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:51:5-75
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:51:22-72
uses-permission#android.permission.READ_SMS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:52:5-67
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:52:22-64
uses-permission#android.permission.SEND_SMS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:53:5-67
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:53:22-64
uses-permission#android.permission.READ_CONTACTS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:54:5-72
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:54:22-69
uses-permission#android.permission.GET_ACCOUNTS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:57:5-71
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:57:22-68
uses-permission#android.permission.MANAGE_ACCOUNTS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:58:5-74
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:58:22-71
uses-permission#android.permission.ACTIVITY_RECOGNITION
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:61:5-79
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:61:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:64:5-79
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:64:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:65:5-81
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:65:22-78
uses-permission#android.permission.BLUETOOTH
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:68:5-68
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:68:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:69:5-74
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:69:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:70:5-76
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:70:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:71:5-73
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:71:22-70
uses-permission#android.permission.BIND_DEVICE_ADMIN
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:74:5-76
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:74:22-73
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:77:5-71
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:77:22-68
uses-permission#android.permission.CAMERA
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:80:5-65
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:80:22-62
uses-feature#android.hardware.sensor.accelerometer
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:5-99
	android:required
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:72-96
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:83:19-71
uses-feature#android.hardware.sensor.light
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:5-91
	android:required
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:64-88
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:84:19-63
uses-feature#android.hardware.sensor.proximity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:5-95
	android:required
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:68-92
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:85:19-67
application
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:87:5-544:19
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:87:5-544:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5ebbf88ef650ff58cf781bd71cd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5ebbf88ef650ff58cf781bd71cd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:10:5-14:19
MERGED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:10:5-14:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82131d01a71b48189bb9697d4f3f129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82131d01a71b48189bb9697d4f3f129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:94:9-35
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:92:9-41
	android:fullBackupContent
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:90:9-54
	tools:ignore
		ADDED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:10:18-69
	android:roundIcon
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:93:9-54
	tools:targetApi
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:96:9-29
	android:icon
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:91:9-43
	android:allowBackup
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:88:9-35
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:95:9-51
	android:dataExtractionRules
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:89:9-65
activity#com.weinuo.quickcommands.MainActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:97:9-114:20
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:100:13-45
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:103:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:99:13-36
	android:resizeableActivity
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:104:13-46
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:101:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:98:13-41
	android:allowEmbedded
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:102:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:105:13-108:29
action#android.intent.action.MAIN
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:106:17-69
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:106:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:107:17-77
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:107:27-74
meta-data#android.app.shortcuts
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:111:13-113:53
	android:resource
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:113:17-50
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:112:17-53
activity#com.weinuo.quickcommands.ui.BubbleActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:117:9-126:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:125:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:119:13-37
	android:resizeableActivity
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:123:13-46
	android:documentLaunchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:122:13-48
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:120:13-55
	android:taskAffinity
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:124:13-36
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:118:13-46
	android:allowEmbedded
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:121:13-41
activity#com.weinuo.quickcommands.ui.recording.GestureRecordingActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:129:9-135:20
	android:screenOrientation
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:133:13-49
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:134:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:131:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:132:13-67
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:130:13-66
activity#com.weinuo.quickcommands.ui.recording.GestureRecordingEditActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:138:9-144:20
	android:screenOrientation
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:142:13-49
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:143:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:140:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:141:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:139:13-70
activity#com.weinuo.quickcommands.shortcut.QuickCommandExecutorActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:147:9-156:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:151:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:149:13-36
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:150:13-67
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:148:13-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:152:13-155:29
action#android.intent.action.VIEW
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:17-69
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:153:25-66
category#android.intent.category.DEFAULT
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:17-76
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:154:27-73
activity#com.weinuo.quickcommands.shortcut.StaticShortcutHandlerActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:159:9-168:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:163:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:161:13-36
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:162:13-67
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:160:13-67
activity#com.weinuo.quickcommands.widget.WidgetClickHandlerActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:171:9-180:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:175:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:173:13-36
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:174:13-67
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:172:13-62
activity#com.weinuo.quickcommands.ui.activities.QuickCommandFormActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:183:9-188:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:187:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:185:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:186:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:184:13-67
activity#com.weinuo.quickcommands.ui.activities.IconSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:191:9-196:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:195:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:193:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:194:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:192:13-64
activity#com.weinuo.quickcommands.ui.activities.AppSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:199:9-204:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:203:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:201:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:202:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:200:13-63
activity#com.weinuo.quickcommands.ui.activities.UnifiedConfigurationActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:207:9-212:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:211:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:209:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:210:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:208:13-71
activity#com.weinuo.quickcommands.ui.activities.DetailedConfigurationActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:215:9-220:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:219:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:217:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:218:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:216:13-72
activity#com.weinuo.quickcommands.ui.activities.ContactSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:223:9-228:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:227:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:225:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:226:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:224:13-67
activity#com.weinuo.quickcommands.ui.activities.ContactGroupSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:231:9-236:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:235:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:233:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:234:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:232:13-72
activity#com.weinuo.quickcommands.ui.activities.RingtoneSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:239:9-244:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:243:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:241:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:242:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:240:13-68
activity#com.weinuo.quickcommands.ui.activities.ShareTargetSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:247:9-252:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:251:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:249:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:250:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:248:13-71
activity#com.weinuo.quickcommands.ui.activities.AdvancedMemoryConfigActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:255:9-260:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:259:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:257:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:258:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:256:13-71
activity#com.weinuo.quickcommands.ui.activities.MemoryLearningDataActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:263:9-268:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:267:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:265:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:266:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:264:13-69
activity#com.weinuo.quickcommands.ui.activities.AdvancedCleanupStrategyActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:271:9-276:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:275:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:273:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:274:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:272:13-74
activity#com.weinuo.quickcommands.ui.activities.AddCleanupRuleActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:279:9-284:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:283:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:281:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:282:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:280:13-65
activity#com.weinuo.quickcommands.ui.activities.SmartReminderDetailConfigActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:287:9-292:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:291:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:289:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:290:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:288:13-76
activity#com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:295:9-300:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:299:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:297:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:298:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:296:13-81
activity#com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:303:9-308:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:307:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:305:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:306:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:304:13-72
activity#com.weinuo.quickcommands.ui.activities.FilterModeSelectionActivity
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:311:9-316:20
	android:launchMode
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:315:13-44
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:313:13-37
	android:theme
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:314:13-55
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:312:13-70
receiver#com.weinuo.quickcommands.widget.OneClickCommandWidget1
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:319:9-329:20
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:322:13-51
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:321:13-36
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:320:13-58
intent-filter#action:name:android.appwidget.action.APPWIDGET_UPDATE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:323:13-325:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:17-84
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:324:25-81
meta-data#android.appwidget.provider
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:326:13-328:75
	android:resource
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:328:17-72
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:327:17-58
receiver#com.weinuo.quickcommands.widget.OneClickCommandWidget2
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:332:9-342:20
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:335:13-51
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:334:13-36
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:333:13-58
receiver#com.weinuo.quickcommands.widget.OneClickCommandWidget3
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:345:9-355:20
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:348:13-51
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:347:13-36
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:346:13-58
receiver#com.weinuo.quickcommands.widget.OneClickCommandWidget4
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:358:9-368:20
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:361:13-51
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:360:13-36
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:359:13-58
receiver#com.weinuo.quickcommands.permission.DeviceAdminReceiver
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:371:9-379:20
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:373:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:372:13-70
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:371:19-65
meta-data#android.app.device_admin
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:374:13-375:56
	android:resource
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:375:17-53
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:374:24-63
intent-filter#action:name:android.app.action.DEVICE_ADMIN_ENABLED
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:376:13-378:29
action#android.app.action.DEVICE_ADMIN_ENABLED
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:377:17-82
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:377:25-79
service#com.weinuo.quickcommands.service.QuickCommandsService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:391:9-395:56
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:393:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:394:13-37
	android:foregroundServiceType
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:395:13-53
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:392:13-57
service#com.weinuo.quickcommands.service.AlarmOverlayService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:400:9-403:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:402:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:403:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:401:13-56
service#com.weinuo.quickcommands.service.TouchBlockOverlayService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:406:9-409:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:408:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:409:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:407:13-61
service#com.weinuo.quickcommands.service.ClipboardRefreshOverlayService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:412:9-415:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:414:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:415:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:413:13-67
service#com.weinuo.quickcommands.service.FloatingButtonService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:418:9-421:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:420:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:421:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:419:13-58
service#com.weinuo.quickcommands.service.SmartReminderOverlayService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:424:9-427:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:426:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:427:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:425:13-64
service#com.weinuo.quickcommands.floating.FloatingRecordingService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:430:9-433:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:432:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:433:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:431:13-62
service#com.weinuo.quickcommands.floating.FloatingAcceleratorService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:436:9-439:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:438:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:439:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:437:13-64
service#com.weinuo.quickcommands.floating.AdvancedFloatingRecordingService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:442:9-445:40
	android:enabled
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:444:13-35
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:445:13-37
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:443:13-70
service#com.weinuo.quickcommands.service.SystemPriorityEnhancementService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:448:9-460:19
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:450:13-63
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:453:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:452:13-79
	android:description
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:451:13-83
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:449:13-69
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:454:13-456:29
action#android.accessibilityservice.AccessibilityService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:17-92
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:455:25-89
meta-data#android.accessibilityservice
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:457:13-459:72
	android:resource
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:459:17-69
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:458:17-60
service#com.weinuo.quickcommands.service.SystemOperationAccessibilityService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:463:9-475:19
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:465:13-80
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:468:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:467:13-79
	android:description
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:466:13-93
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:464:13-72
service#com.weinuo.quickcommands.service.InterfaceInteractionAccessibilityService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:478:9-490:19
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:480:13-85
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:483:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:482:13-79
	android:description
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:481:13-98
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:479:13-77
service#com.weinuo.quickcommands.service.GestureRecognitionAccessibilityService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:493:9-505:19
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:495:13-83
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:498:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:497:13-79
	android:description
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:496:13-96
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:494:13-75
service#com.weinuo.quickcommands.service.AutoClickerAccessibilityService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:508:9-520:19
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:510:13-76
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:513:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:512:13-79
	android:description
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:511:13-89
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:509:13-68
service#com.weinuo.quickcommands.service.QuickCommandsNotificationService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:523:9-532:19
	android:label
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:525:13-71
	android:exported
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:528:13-36
	android:permission
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:527:13-87
	android:description
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:526:13-84
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:524:13-69
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:529:13-531:29
action#android.service.notification.NotificationListenerService
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:530:17-99
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:530:25-96
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:540:13-542:63
	android:resource
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:542:17-60
	android:name
		ADDED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml:541:17-67
uses-sdk
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaadb670fa6ca1f2c5a47855923454a7\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aaadb670fa6ca1f2c5a47855923454a7\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff597674a64920f525161048cb2bedc7\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff597674a64920f525161048cb2bedc7\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfead65ab0070d3a5c6953a3123d6569\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfead65ab0070d3a5c6953a3123d6569\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd4296f3379786dc23425ecfd7130bda\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd4296f3379786dc23425ecfd7130bda\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b00bc502ebac934934628c5371eb11c\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b00bc502ebac934934628c5371eb11c\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [dev.chrisbanes.haze:haze-materials-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967dbd860d89950624e0783b84d2948b\transformed\haze-materials-release\AndroidManifest.xml:5:5-44
MERGED from [dev.chrisbanes.haze:haze-materials-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967dbd860d89950624e0783b84d2948b\transformed\haze-materials-release\AndroidManifest.xml:5:5-44
MERGED from [dev.chrisbanes.haze:haze-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\224d0965d174a3d80cb3c280827d76f6\transformed\haze-release\AndroidManifest.xml:5:5-44
MERGED from [dev.chrisbanes.haze:haze-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\224d0965d174a3d80cb3c280827d76f6\transformed\haze-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbe454da14384bb37833dcce63330c70\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbe454da14384bb37833dcce63330c70\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad2279977a0ab9a9faf3d3cd6fc86c57\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad2279977a0ab9a9faf3d3cd6fc86c57\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08c9225172fa96963d92686bc80c5e0d\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08c9225172fa96963d92686bc80c5e0d\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c36b8cfd8af20acf52fd20004c02f0b3\transformed\material-ripple\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c36b8cfd8af20acf52fd20004c02f0b3\transformed\material-ripple\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2368c71bd8b2fc1d6b9fcb9e81292ba7\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2368c71bd8b2fc1d6b9fcb9e81292ba7\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dea914038927ff52700e9f966bf73df2\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dea914038927ff52700e9f966bf73df2\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f503a90f72cbab575267a43a3d872ea8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f503a90f72cbab575267a43a3d872ea8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\708ad0d2216a5a4c7b8c8fa24fa83ad6\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\708ad0d2216a5a4c7b8c8fa24fa83ad6\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\342a711617df3294d50f3bbed1b43a2a\transformed\ui-backhandler-debug\AndroidManifest.xml:5:5-44
MERGED from [org.jetbrains.compose.ui:ui-backhandler-android-debug:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\342a711617df3294d50f3bbed1b43a2a\transformed\ui-backhandler-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb470c2bb9bb72c2a693b436558d35ae\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb470c2bb9bb72c2a693b436558d35ae\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\950570e248363716e52dbb1ed2521b7d\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\950570e248363716e52dbb1ed2521b7d\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948e4c45da24d54e414534fe9f255021\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948e4c45da24d54e414534fe9f255021\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd680ebde1bb8e519f1434c60228fb73\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd680ebde1bb8e519f1434c60228fb73\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09fc0498259161a6170aac6fff13c700\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09fc0498259161a6170aac6fff13c700\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a24334a9e370adf40f880ddbdf99bd1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a24334a9e370adf40f880ddbdf99bd1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\698e5422ce58bab0678db2e269f4f608\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\698e5422ce58bab0678db2e269f4f608\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91ab27a6fa87b1fe11e96fb74e707ad\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91ab27a6fa87b1fe11e96fb74e707ad\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76e94506b8e1b5f2623e7170a4659e66\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76e94506b8e1b5f2623e7170a4659e66\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6aeeaf2ff80bf712f2b27bd52fe7dcc\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6aeeaf2ff80bf712f2b27bd52fe7dcc\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06f299a7430b89abd87e700959100d99\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06f299a7430b89abd87e700959100d99\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\726a3fae8cb1d143e422f6a4eab9f297\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\726a3fae8cb1d143e422f6a4eab9f297\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be4f9c0dfec74063ff30fa08a680dd9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be4f9c0dfec74063ff30fa08a680dd9\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\576f52a3554e7648cf6b75e678e147f1\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\576f52a3554e7648cf6b75e678e147f1\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b7e2babe290c28fce233716d8dc30e9\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b7e2babe290c28fce233716d8dc30e9\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da0e8e9b02353ac2aa7e20fc8b08751\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2da0e8e9b02353ac2aa7e20fc8b08751\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c3cfe4d2b3a318dc0da05d36ddb2438\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c3cfe4d2b3a318dc0da05d36ddb2438\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\194dfe6f58810a08411bb9e62e3f1f5f\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\194dfe6f58810a08411bb9e62e3f1f5f\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de8c3361cfa36875b3f5a93145f7d0c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7de8c3361cfa36875b3f5a93145f7d0c\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e109b7321dece0f02b118f7abb21702\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3e109b7321dece0f02b118f7abb21702\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c113be44b87c52c5b16adf2cbe56d5c7\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c113be44b87c52c5b16adf2cbe56d5c7\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bca9768a5df952a51293a04aaab9d5\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bca9768a5df952a51293a04aaab9d5\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7eaf11704a7bef50ed7e94aaf38b22\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea7eaf11704a7bef50ed7e94aaf38b22\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\245ddc52bcc53f88b16e61cd6a9cc20c\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\245ddc52bcc53f88b16e61cd6a9cc20c\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f703f5e37e09a1bf226bd79386dc674\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f703f5e37e09a1bf226bd79386dc674\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58ff5ea23647d58c7bc82fa2455ad617\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58ff5ea23647d58c7bc82fa2455ad617\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ead721738e64af23153ea0abb53ae251\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ead721738e64af23153ea0abb53ae251\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbaf997ae37e03272ee78df9992f8934\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbaf997ae37e03272ee78df9992f8934\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5ebbf88ef650ff58cf781bd71cd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5ebbf88ef650ff58cf781bd71cd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5632ccc8165adbb75e6e26adc7a4728e\transformed\tracing-ktx-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5632ccc8165adbb75e6e26adc7a4728e\transformed\tracing-ktx-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9055cc13851f5b771be299dccd8bdd19\transformed\tracing\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9055cc13851f5b771be299dccd8bdd19\transformed\tracing\AndroidManifest.xml:20:5-44
MERGED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:6:5-44
MERGED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:6:5-44
MERGED from [dev.rikka.shizuku:api:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99762b566a13ea95b9c52939a4e42cab\transformed\api-13.1.5\AndroidManifest.xml:5:5-44
MERGED from [dev.rikka.shizuku:api:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\99762b566a13ea95b9c52939a4e42cab\transformed\api-13.1.5\AndroidManifest.xml:5:5-44
MERGED from [dev.rikka.shizuku:shared:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f773e4b67745be34e493b38028a4378c\transformed\shared-13.1.5\AndroidManifest.xml:5:5-44
MERGED from [dev.rikka.shizuku:shared:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f773e4b67745be34e493b38028a4378c\transformed\shared-13.1.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923208cb488569d83b984d91e48f74d5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\923208cb488569d83b984d91e48f74d5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82131d01a71b48189bb9697d4f3f129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82131d01a71b48189bb9697d4f3f129f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\968b4e2f78df6ff10b2d5223881906ae\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\968b4e2f78df6ff10b2d5223881906ae\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [dev.rikka.shizuku:aidl:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ff1e83b9d7cd53a3c741fa73e69ec1d\transformed\aidl-13.1.5\AndroidManifest.xml:5:5-44
MERGED from [dev.rikka.shizuku:aidl:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ff1e83b9d7cd53a3c741fa73e69ec1d\transformed\aidl-13.1.5\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Users\Cai_Mouhui\Documents\BackgroundManagerMD3\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5ebbf88ef650ff58cf781bd71cd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1a60f5ebbf88ef650ff58cf781bd71cd\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d27b9367432edd9869b9297aaff418e3\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.weinuo.quickcommands.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb17b206e660e96f6900b88209c2dff8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32d35540cb17a90796eacb79dc150b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\402d4556df337854d3559e73e38f81c3\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
uses-permission#moe.shizuku.manager.permission.API_V23
ADDED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:8:5-78
	android:name
		ADDED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:8:22-75
meta-data#moe.shizuku.client.V3_SUPPORT
ADDED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:11:9-13:36
	android:value
		ADDED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:13:13-33
	android:name
		ADDED from [dev.rikka.shizuku:provider:13.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8c327978b339642567683fb94fb9dc5\transformed\provider-13.1.5\AndroidManifest.xml:12:13-57
