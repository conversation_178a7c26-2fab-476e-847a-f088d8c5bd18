package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import com.weinuo.quickcommands.utils.ContactsHelper
import com.weinuo.quickcommands.ui.screens.ContactGroupSelectionMode
import com.weinuo.quickcommands.ui.screens.ContactGroupSelectionScreen
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.storage.ContactSelectionStorageManager

/**
 * 联系人分组选择Activity
 * 
 * 提供独立的联系人分组选择界面，支持单选模式
 * 替代原有的NavController导航方式，提供更好的用户体验
 */
class ContactGroupSelectionActivity : ComponentActivity() {
    
    companion object {
        private const val EXTRA_SELECTION_MODE = "selection_mode"
        private const val EXTRA_SELECTED_GROUP_ID = "selected_group_id"
        private const val EXTRA_RESULT_KEY = "result_key"
        const val RESULT_SELECTED_GROUP = "selected_group"

        /**
         * 启动单选联系人分组界面
         */
        fun startForSingleSelection(
            context: Context,
            selectedGroupId: String = "",
            resultKey: String = "selected_group"
        ) {
            val intent = Intent(context, ContactGroupSelectionActivity::class.java).apply {
                putExtra(EXTRA_SELECTION_MODE, ContactGroupSelectionMode.SINGLE.name)
                putExtra(EXTRA_SELECTED_GROUP_ID, selectedGroupId)
                putExtra(EXTRA_RESULT_KEY, resultKey)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val selectionModeString = intent.getStringExtra(EXTRA_SELECTION_MODE) ?: ContactGroupSelectionMode.SINGLE.name
        val selectionMode = ContactGroupSelectionMode.valueOf(selectionModeString)
        val selectedGroupId = intent.getStringExtra(EXTRA_SELECTED_GROUP_ID) ?: ""
        val resultKey = intent.getStringExtra(EXTRA_RESULT_KEY) ?: "selected_group"
        
        setContent {
            QuickCommandsTheme {
                ContactGroupSelectionActivityContent(
                    selectionMode = selectionMode,
                    initialSelectedGroupId = selectedGroupId,
                    onGroupSelected = { selectedGroup ->
                        finishWithResult(selectedGroup, resultKey)
                    },
                    onFinish = { finish() }
                )
            }
        }
    }

    /**
     * 完成选择并返回结果
     */
    private fun finishWithResult(selectedGroup: ContactsHelper.ContactGroup, resultKey: String) {
        // 将选择结果存储到ContactSelectionStorageManager
        ContactSelectionStorageManager.storeSelectedContactGroup(resultKey, selectedGroup)

        // 设置Activity结果
        val resultIntent = Intent().apply {
            putExtra(RESULT_SELECTED_GROUP, selectedGroup.id)
        }
        setResult(Activity.RESULT_OK, resultIntent)
        finish()
    }
}

/**
 * 联系人分组选择Activity的内容组件
 * 
 * 不依赖NavController的可复用组件，可以在Activity中使用
 */
@Composable
fun ContactGroupSelectionActivityContent(
    selectionMode: ContactGroupSelectionMode,
    initialSelectedGroupId: String = "",
    onGroupSelected: (ContactsHelper.ContactGroup) -> Unit,
    onFinish: () -> Unit
) {
    ContactGroupSelectionScreen(
        selectionMode = selectionMode,
        initialSelectedGroupId = initialSelectedGroupId,
        onGroupSelected = onGroupSelected,
        onDismiss = onFinish
    )
}
